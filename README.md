# ICP Swap 套利系统

基于ICPSwap V3协议的高性能套利交易系统，使用Rust实现，支持自动检测和执行套利机会。

## 核心功能

### 1. 本地数据管理
- 从JSON文件加载交易池信息到内存
- 高效的数据结构支持快速查询和更新
- 定期数据持久化和同步机制

### 2. 套利机会检测
- 优化版Bellman-Ford/SPFA算法检测负环
- 监控池子交易事件触发套利计算
- 可配置的套利阈值过滤

### 3. 最优交易量计算
- 三分搜索算法精确计算最大化利润的交易量
- 考虑滑点、手续费等因素
- 回测功能验证计算准确性

### 4. 价格计算优化
- 基于UniswapV3算法本地计算池子价格
- 价格缓存机制和异常检测
- 避免频繁请求节点

## 技术架构

### 模块化设计
```
src/
├── config/          # 配置管理
├── data/            # 数据管理
├── algorithms/      # 算法实现
├── execution/       # 交易执行
├── monitoring/      # 监控日志
└── types/           # 类型定义
```

### 核心算法
- **Bellman-Ford算法**: 检测套利机会（负环检测）
- **三分搜索算法**: 寻找最优交易量
- **UniswapV3价格计算**: 精确的价格和滑点计算

### 安全措施
- 交易限额控制
- 紧急停止机制
- 风险管理和止损
- 防重复交易

## 快速开始

### 1. 环境要求
- Rust 1.70+
- Tokio异步运行时

### 2. 安装依赖
```bash
cargo build --release
```

### 3. 配置系统
编辑 `config.json` 文件或设置环境变量：

```json
{
  "arbitrage": {
    "min_profit_threshold": "10.0",
    "min_profit_percentage": "0.01",
    "max_trade_amount": "100000.0",
    "confidence_threshold": 0.8
  },
  "risk": {
    "max_position_size": "50000.0",
    "max_daily_loss": "1000.0",
    "emergency_stop_enabled": true
  }
}
```

### 4. 准备池子数据
系统首次运行会自动创建示例池子数据文件 `data/pools.json`。

### 5. 运行系统
```bash
# 使用配置文件
cargo run --release

# 或使用环境变量
export LOG_LEVEL=debug
export MIN_PROFIT_THRESHOLD=5.0
cargo run --release
```

## 配置说明

### 套利配置
- `min_profit_threshold`: 最小利润阈值
- `min_profit_percentage`: 最小利润百分比
- `max_trade_amount`: 最大交易金额
- `confidence_threshold`: 置信度阈值

### 风险管理
- `max_position_size`: 最大仓位大小
- `max_daily_loss`: 最大日损失
- `stop_loss_percentage`: 止损百分比
- `emergency_stop_enabled`: 紧急停止开关

### 执行配置
- `max_concurrent_trades`: 最大并发交易数
- `trade_timeout_secs`: 交易超时时间

## 监控和日志

### 结构化日志
系统使用结构化JSON日志，包含：
- 套利机会检测事件
- 交易执行结果
- 风险管理事件
- 性能指标

### 指标收集
- 总套利机会数
- 执行交易数和成功率
- 总利润和平均利润
- 系统性能指标

### 示例日志
```json
{
  "event": "opportunity_detected",
  "opportunity_id": "uuid",
  "path": {
    "tokens": ["ICP", "ckBTC", "ICP"],
    "pools": ["pool1", "pool2"]
  },
  "metrics": {
    "profit": "15.5",
    "confidence": 0.85
  }
}
```

## 开发指南

### 添加新的套利策略
1. 在 `algorithms/` 目录下创建新的检测器
2. 实现 `ArbitrageDetector` trait
3. 在主程序中注册新策略

### 扩展风险管理
1. 修改 `execution/risk_manager.rs`
2. 添加新的风险评估规则
3. 更新配置结构

### 自定义价格计算
1. 扩展 `data/price_calculator.rs`
2. 实现特定的价格计算逻辑
3. 集成到池子管理器中

## 性能优化

### 内存优化
- 使用 `DashMap` 实现高并发缓存
- 定期清理过期数据
- 避免频繁内存分配

### 并发优化
- 异步处理提高吞吐量
- 信号量控制并发数量
- 批处理减少资源消耗

### 网络优化
- 连接池复用
- 请求重试机制
- 超时控制

## 故障排除

### 常见问题
1. **配置加载失败**: 检查配置文件格式和路径
2. **池子数据错误**: 验证JSON格式和数据完整性
3. **网络连接问题**: 检查IC网关URL和网络连接
4. **权限问题**: 确保有足够的文件读写权限

### 调试模式
```bash
export LOG_LEVEL=debug
cargo run
```

### 性能分析
```bash
cargo build --release
perf record ./target/release/icpswap-arb
perf report
```

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 创建 Pull Request

## 许可证

MIT License

## 联系方式

如有问题或建议，请创建 Issue 或联系开发团队。
