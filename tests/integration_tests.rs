/// ICPSwap V3套利系统集成测试
/// 
/// 这个测试套件验证整个套利系统的功能，包括：
/// - 系统初始化和启动
/// - 数据更新机制
/// - 套利检测算法
/// - 性能和稳定性
use icpswap_arb::simplified_arbitrage::{
    SimplifiedArbitrageSystem, ArbitrageConfig, PoolConfig
};
use tokio::time::{Duration, timeout};
use std::sync::Arc;

/// 测试系统初始化
#[tokio::test]
async fn test_system_initialization() {
    let config = create_test_config();
    let system = SimplifiedArbitrageSystem::new(config);
    
    // 测试系统启动
    let result = timeout(Duration::from_secs(5), system.start()).await;
    assert!(result.is_ok(), "系统启动应该成功");
    
    // 等待初始化完成
    tokio::time::sleep(Duration::from_millis(100)).await;
    
    // 验证池子已初始化
    let pools = system.get_pools().await;
    assert!(!pools.is_empty(), "应该有池子被初始化");
    assert_eq!(pools.len(), 3, "应该有3个测试池子");
}

/// 测试数据更新机制
#[tokio::test]
async fn test_data_update_mechanism() {
    let config = create_test_config();
    let system = SimplifiedArbitrageSystem::new(config);
    
    // 启动系统
    system.start().await.expect("系统启动失败");
    
    // 等待数据更新
    tokio::time::sleep(Duration::from_secs(2)).await;
    
    let pools = system.get_pools().await;
    
    // 验证数据已更新
    for pool in pools.values() {
        assert!(pool.sqrt_price_x96 > 0, "价格应该已更新");
        assert!(pool.liquidity > 0, "流动性应该已更新");
    }
}

/// 测试套利检测功能
#[tokio::test]
async fn test_arbitrage_detection() {
    let config = create_test_config();
    let system = SimplifiedArbitrageSystem::new(config);
    
    // 启动系统
    system.start().await.expect("系统启动失败");
    
    // 等待系统运行一段时间
    tokio::time::sleep(Duration::from_secs(3)).await;
    
    // 验证系统正在运行
    let stats = system.get_stats().await;
    assert!(stats.total_pools > 0, "应该有池子在运行");
    assert!(stats.active_pools >= 0, "活跃池子数应该合理");
}

/// 测试系统统计功能
#[tokio::test]
async fn test_system_statistics() {
    let config = create_test_config();
    let system = SimplifiedArbitrageSystem::new(config);
    
    // 启动系统
    system.start().await.expect("系统启动失败");
    
    // 等待初始化
    tokio::time::sleep(Duration::from_millis(500)).await;
    
    let stats = system.get_stats().await;
    
    // 验证统计信息
    assert_eq!(stats.total_pools, 3, "总池子数应该是3");
    assert!(stats.total_liquidity >= 0.0, "总流动性应该非负");
    assert!(stats.avg_price_impact >= 0.0, "平均价格影响应该非负");
    assert!(stats.avg_price_impact <= 1.0, "平均价格影响应该小于100%");
}

/// 测试并发安全性
#[tokio::test]
async fn test_concurrent_safety() {
    let config = create_test_config();
    let system = Arc::new(SimplifiedArbitrageSystem::new(config));
    
    // 启动系统
    system.start().await.expect("系统启动失败");
    
    // 创建多个并发任务
    let mut handles = Vec::new();
    
    for i in 0..10 {
        let system_clone = system.clone();
        let handle = tokio::spawn(async move {
            for _ in 0..5 {
                let _pools = system_clone.get_pools().await;
                let _stats = system_clone.get_stats().await;
                tokio::time::sleep(Duration::from_millis(10)).await;
            }
            i
        });
        handles.push(handle);
    }
    
    // 等待所有任务完成
    for handle in handles {
        let result = handle.await;
        assert!(result.is_ok(), "并发任务应该成功完成");
    }
}

/// 测试错误处理
#[tokio::test]
async fn test_error_handling() {
    // 测试空配置
    let empty_config = ArbitrageConfig {
        min_profit_threshold: 0.01,
        max_trade_amount: 100.0,
        update_interval_secs: 1,
        enabled_pools: vec![], // 空池子列表
    };
    
    let system = SimplifiedArbitrageSystem::new(empty_config);
    
    // 系统应该能够处理空配置
    let result = system.start().await;
    assert!(result.is_ok(), "系统应该能处理空配置");
    
    let stats = system.get_stats().await;
    assert_eq!(stats.total_pools, 0, "空配置应该有0个池子");
}

/// 测试配置验证
#[tokio::test]
async fn test_configuration_validation() {
    // 测试极端配置值
    let extreme_config = ArbitrageConfig {
        min_profit_threshold: 0.0, // 0%利润阈值
        max_trade_amount: 0.0,      // 0交易金额
        update_interval_secs: 1,    // 1秒更新
        enabled_pools: create_test_pools(),
    };
    
    let system = SimplifiedArbitrageSystem::new(extreme_config);
    let result = system.start().await;
    assert!(result.is_ok(), "系统应该能处理极端配置");
}

/// 性能基准测试
#[tokio::test]
async fn test_performance_benchmark() {
    let config = create_test_config();
    let system = SimplifiedArbitrageSystem::new(config);
    
    // 启动系统
    system.start().await.expect("系统启动失败");
    
    // 等待系统稳定
    tokio::time::sleep(Duration::from_millis(500)).await;
    
    // 测试获取池子数据的性能
    let start = std::time::Instant::now();
    for _ in 0..100 {
        let _pools = system.get_pools().await;
    }
    let duration = start.elapsed();
    
    // 验证性能要求（100次调用应该在1秒内完成）
    assert!(duration < Duration::from_secs(1), 
            "100次池子数据获取应该在1秒内完成，实际用时: {:?}", duration);
    
    // 测试统计信息获取性能
    let start = std::time::Instant::now();
    for _ in 0..50 {
        let _stats = system.get_stats().await;
    }
    let duration = start.elapsed();
    
    assert!(duration < Duration::from_millis(500), 
            "50次统计信息获取应该在500ms内完成，实际用时: {:?}", duration);
}

/// 测试内存使用
#[tokio::test]
async fn test_memory_usage() {
    let config = create_test_config();
    let system = SimplifiedArbitrageSystem::new(config);
    
    // 启动系统
    system.start().await.expect("系统启动失败");
    
    // 运行一段时间以观察内存使用
    for _ in 0..10 {
        let _pools = system.get_pools().await;
        let _stats = system.get_stats().await;
        tokio::time::sleep(Duration::from_millis(100)).await;
    }
    
    // 验证系统仍然响应
    let final_stats = system.get_stats().await;
    assert!(final_stats.total_pools > 0, "系统应该仍然正常运行");
}

/// 创建测试配置
fn create_test_config() -> ArbitrageConfig {
    ArbitrageConfig {
        min_profit_threshold: 0.005, // 0.5%
        max_trade_amount: 100.0,
        update_interval_secs: 1, // 快速更新用于测试
        enabled_pools: create_test_pools(),
    }
}

/// 创建测试池子配置
fn create_test_pools() -> Vec<PoolConfig> {
    vec![
        PoolConfig {
            id: "TEST-ICP-ckBTC".to_string(),
            canister_id: "rdmx6-jaaaa-aaaah-qcaiq-cai".to_string(),
            token0_symbol: "ICP".to_string(),
            token1_symbol: "ckBTC".to_string(),
            enabled: true,
        },
        PoolConfig {
            id: "TEST-ICP-ckETH".to_string(),
            canister_id: "xkbqi-6qaaa-aaaah-qcaiq-cai".to_string(),
            token0_symbol: "ICP".to_string(),
            token1_symbol: "ckETH".to_string(),
            enabled: true,
        },
        PoolConfig {
            id: "TEST-ckBTC-ckETH".to_string(),
            canister_id: "5hr3g-hqaaa-aaaah-qcaiq-cai".to_string(),
            token0_symbol: "ckBTC".to_string(),
            token1_symbol: "ckETH".to_string(),
            enabled: true,
        },
    ]
}
