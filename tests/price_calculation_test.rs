use icpswap_arb::{
    data::PriceCalculator,
    types::*,
};
use rust_decimal::Decimal;
use num_traits::{FromPrimitive, ToPrimitive};

/// 测试价格计算的准确性
#[tokio::test]
async fn test_sqrt_price_x96_to_price_accuracy() {
    println!("🧮 测试sqrtPriceX96到价格的转换准确性");
    
    // 测试用例：基于ICPSwap V3的实际数据
    let test_cases = vec![
        // (sqrtPriceX96, decimals0, decimals1, expected_price_range)
        (11457486222758324355242039155176u128, 8, 8, (20913.0, 20914.0)), // 标准价格1.0附近
        (112045541949572279837463876454u128, 8, 8, (1.9, 2.1)), // 价格约2.0
        (56022770974786139918731938227u128, 8, 8, (0.4, 0.6)), // 价格约0.5
        (158456325028528675187087900672u128, 8, 18, (3.9e10, 4.1e10)), // 不同精度，考虑10位小数差异
    ];

    for (i, (sqrt_price_x96, decimals0, decimals1, (min_price, max_price))) in test_cases.iter().enumerate() {
        let result = PriceCalculator::sqrt_price_x96_to_price(*sqrt_price_x96, *decimals0, *decimals1);
        assert!(result.is_ok(), "测试用例 {} 计算失败: {:?}", i, result);
        let price = result.unwrap();
        let price_f64 = price.to_f64().unwrap();
        
        println!("测试用例 {}: sqrtPriceX96={}, 计算价格={}, 预期范围=[{}, {}]", 
                i, sqrt_price_x96, price_f64, min_price, max_price);
        
        assert!(price_f64 >= *min_price && price_f64 <= *max_price, 
               "价格 {} 不在预期范围 [{}, {}] 内", price_f64, min_price, max_price);
    }
}

#[tokio::test]
async fn test_tick_to_price_accuracy() {
    println!("🎯 测试tick到价格的转换准确性");
    
    let test_cases = vec![
        // (tick, decimals0, decimals1, expected_price_range)
        (0, 8, 8, (0.99, 1.01)), // tick=0对应价格1.0
        (6932, 8, 8, (1.99, 2.01)), // tick=6932对应价格约2.0
        (-6932, 8, 8, (0.49, 0.51)), // tick=-6932对应价格约0.5
        (13863, 8, 8, (3.99, 4.01)), // tick=13863对应价格约4.0
    ];
    
    for (i, (tick, decimals0, decimals1, (min_price, max_price))) in test_cases.iter().enumerate() {
        let result = PriceCalculator::tick_to_price(*tick, *decimals0, *decimals1);
        assert!(result.is_ok(), "测试用例 {} 计算失败: {:?}", i, result);
        
        let price = result.unwrap();
        let price_f64 = price.to_f64().unwrap();
        
        println!("测试用例 {}: tick={}, 计算价格={}, 预期范围=[{}, {}]", 
                i, tick, price_f64, min_price, max_price);
        
        assert!(price_f64 >= *min_price && price_f64 <= *max_price, 
               "价格 {} 不在预期范围 [{}, {}] 内", price_f64, min_price, max_price);
    }
}

#[tokio::test]
async fn test_swap_calculation_accuracy() {
    println!("💱 测试swap计算的准确性");
    
    // 创建测试池子状态
    let pool_state = create_test_pool_with_real_data();
    
    let test_cases = vec![
        // (amount_in, expected_min_out, expected_max_out)
        // 基于实际价格约20913 ICP/ckBTC，扣除0.3%手续费后的预期输出
        // 1 ckBTC (100000000 satoshi) -> ~20913 ICP (考虑手续费和滑点)
        (Decimal::from(100000000), Decimal::from(2000000000000i64), Decimal::from(2100000000000i64)), // 1 ckBTC -> ~20913 ICP
    ];
    
    for (i, (amount_in, min_out, max_out)) in test_cases.iter().enumerate() {
        // 测试token0 -> token1方向
        let result = PriceCalculator::calculate_amount_out_with_direction(*amount_in, &pool_state, true);
        assert!(result.is_ok(), "测试用例 {} (token0->token1) 计算失败: {:?}", i, result);

        let amount_out = result.unwrap();

        println!("测试用例 {} (token0->token1): 输入={}, 输出={}, 预期范围=[{}, {}]",
                i, amount_in, amount_out, min_out, max_out);

        assert!(amount_out >= *min_out && amount_out <= *max_out,
               "输出金额 {} 不在预期范围 [{}, {}] 内", amount_out, min_out, max_out);

        // 测试token1 -> token0方向
        let result_reverse = PriceCalculator::calculate_amount_out_with_direction(*amount_in, &pool_state, false);
        assert!(result_reverse.is_ok(), "测试用例 {} (token1->token0) 计算失败: {:?}", i, result_reverse);

        let amount_out_reverse = result_reverse.unwrap();
        println!("测试用例 {} (token1->token0): 输入={}, 输出={}",
                i, amount_in, amount_out_reverse);
    }
}

#[tokio::test]
async fn test_exact_swap_calculation() {
    println!("🎯 测试精确swap计算");

    let pool_state = create_test_pool_with_real_data();
    let amount_in = Decimal::from(10000000000i64); // 1 ics

    // 详细分析计算过程
    println!("池子信息:");
    println!("  Token0: {} ({})", pool_state.metadata.token0.symbol, pool_state.metadata.token0.decimals);
    println!("  Token1: {} ({})", pool_state.metadata.token1.symbol, pool_state.metadata.token1.decimals);
    println!("  sqrtPriceX96: {}", pool_state.metadata.sqrt_price_x96);
    println!("  流动性: {}", pool_state.metadata.liquidity);
    println!("  手续费: {}bp", pool_state.metadata.fee);
    println!("  计算价格: {}", pool_state.price);

    // 手动计算期望结果
    let fee_rate = pool_state.metadata.fee as f64 / 1_000_000.0;
    let amount_in_f64 = amount_in.to_f64().unwrap();
    let fee_amount = amount_in_f64 * fee_rate;
    let amount_after_fee = amount_in_f64 - fee_amount;
    let expected_output = amount_after_fee * pool_state.price.to_f64().unwrap();

    println!("手动计算:");
    println!("  输入金额: {}", amount_in_f64);
    println!("  手续费: {}", fee_amount);
    println!("  扣费后金额: {}", amount_after_fee);
    println!("  价格: {}", pool_state.price.to_f64().unwrap());
    println!("  理论输出: {}", expected_output);

    // 测试token0 -> token1
    let result = PriceCalculator::calculate_exact_swap_output(amount_in, &pool_state, true);
    assert!(result.is_ok(), "精确swap计算失败: {:?}", result);

    let swap_result = result.unwrap();
    println!("Token0->Token1 Swap结果:");
    println!("  输入金额: {}", amount_in);
    println!("  输出金额: {}", swap_result.amount_out);
    println!("  新sqrtPriceX96: {}", swap_result.new_sqrt_price_x96);
    println!("  价格影响: {:.4}%", swap_result.price_impact * Decimal::from(100));
    println!("  手续费: {}", swap_result.fee_amount);

    // 尝试不同的价格理解
    let sqrt_price_x96 = pool_state.metadata.sqrt_price_x96 as f64;
    let q96 = 2_f64.powi(96);
    let sqrt_price = sqrt_price_x96 / q96;
    let raw_price = sqrt_price * sqrt_price;
    println!("原始价格 (无小数调整): {}", raw_price);
    println!("原始价格输出: {}", amount_after_fee * raw_price);

    // 调试UniswapV3计算
    println!("调试UniswapV3计算:");
    println!("  当前sqrtPrice: {}", sqrt_price);
    println!("  流动性: {}", pool_state.metadata.liquidity);
    println!("  输入金额(扣费后): {}", amount_after_fee);

    // 手动计算新的sqrtPrice
    let liquidity_f64 = pool_state.metadata.liquidity as f64;
    let numerator = liquidity_f64 * sqrt_price;
    let denominator = liquidity_f64 + amount_after_fee * sqrt_price;
    let sqrt_price_new = numerator / denominator;
    println!("  新sqrtPrice: {}", sqrt_price_new);

    // 手动计算输出
    let manual_output = liquidity_f64 * (sqrt_price_new - sqrt_price);
    println!("  手动计算输出: {}", manual_output);

    // 验证结果合理性
    assert!(swap_result.amount_out > Decimal::ZERO, "输出金额应该大于0");
    assert!(swap_result.price_impact >= Decimal::ZERO, "价格影响应该非负");
    assert!(swap_result.fee_amount > Decimal::ZERO, "手续费应该大于0");
    assert!(swap_result.new_sqrt_price_x96 > 0, "新价格应该大于0");
}

#[tokio::test]
async fn test_tick_sqrt_price_conversion() {
    println!("🔄 测试tick和sqrtPriceX96的相互转换");
    
    let test_ticks = vec![0, 1000, -1000, 10000, -10000];
    
    for tick in test_ticks {
        // tick -> sqrtPriceX96
        let sqrt_price_x96_result = PriceCalculator::tick_to_sqrt_price_x96(tick);
        assert!(sqrt_price_x96_result.is_ok(), "tick {} 转换失败: {:?}", tick, sqrt_price_x96_result);
        
        let sqrt_price_x96 = sqrt_price_x96_result.unwrap();
        
        // sqrtPriceX96 -> tick
        let tick_result = PriceCalculator::sqrt_price_x96_to_tick(sqrt_price_x96);
        assert!(tick_result.is_ok(), "sqrtPriceX96 {} 转换失败: {:?}", sqrt_price_x96, tick_result);
        
        let converted_tick = tick_result.unwrap();
        
        println!("原始tick: {}, sqrtPriceX96: {}, 转换回tick: {}", 
                tick, sqrt_price_x96, converted_tick);
        
        // 允许小的误差（由于浮点数精度）
        let tick_diff = (tick - converted_tick).abs();
        assert!(tick_diff <= 1, "tick转换误差过大: 原始={}, 转换={}, 误差={}", 
               tick, converted_tick, tick_diff);
    }
}

#[tokio::test]
async fn test_price_impact_calculation() {
    println!("📊 测试价格影响计算");
    
    let pool_state = create_test_pool_with_real_data();
    
    // 测试不同交易量的价格影响
    let amounts = vec![
        Decimal::from(100),    // 小额
        Decimal::from(1000),   // 中等
        Decimal::from(10000),  // 大额
        Decimal::from(100000), // 超大额
    ];
    
    let mut last_impact = Decimal::ZERO;
    
    for amount in amounts {
        let result = PriceCalculator::calculate_exact_swap_output(amount, &pool_state, true);
        assert!(result.is_ok(), "计算失败: {:?}", result);
        
        let swap_result = result.unwrap();
        let impact_percent = swap_result.price_impact * Decimal::from(100);
        
        println!("交易量: {}, 价格影响: {:.4}%", amount, impact_percent);
        
        // 验证价格影响随交易量增加而增加
        if last_impact > Decimal::ZERO {
            assert!(swap_result.price_impact >= last_impact, 
                   "价格影响应该随交易量增加: 当前={:.6}, 上一个={:.6}", 
                   swap_result.price_impact, last_impact);
        }
        
        last_impact = swap_result.price_impact;
    }
}

#[tokio::test]
async fn test_slippage_calculation() {
    println!("📉 测试滑点计算");
    
    let pool_state = create_test_pool_with_real_data();
    
    let test_amounts = vec![
        Decimal::from(100),
        Decimal::from(1000),
        Decimal::from(10000),
    ];
    
    for amount in test_amounts {
        let result = PriceCalculator::calculate_slippage(amount, &pool_state);
        assert!(result.is_ok(), "滑点计算失败: {:?}", result);
        
        let slippage = result.unwrap();
        let slippage_percent = slippage * Decimal::from(100);
        
        println!("交易量: {}, 滑点: {:.4}%", amount, slippage_percent);
        
        // 验证滑点在合理范围内
        assert!(slippage >= Decimal::ZERO, "滑点应该非负");
        assert!(slippage <= Decimal::from_f64(0.5).unwrap(), "滑点不应超过50%");
    }
}

// 辅助函数：创建基于真实数据的测试池子
fn create_test_pool_with_real_data() -> PoolState {
    let icp_token = Token {
        address: "rrkah-fqaaa-aaaaa-aaaaq-cai".to_string(),
        standard: "ICRC1".to_string(),
        symbol: "ICP".to_string(),
        decimals: 8,
    };
    
    let ics_token = Token {
        address: "mxzaz-hqaaa-aaaar-qaada-cai".to_string(),
        standard: "ICRC2".to_string(),
        symbol: "ICS".to_string(),
        decimals: 8,
    };
    
    // 使用更真实的池子数据
    let metadata = PoolMetadata {
        key: "ICP-ckBTC-3000".to_string(),
        token0: ics_token,
        token1: icp_token,
        fee: 3000, // 0.3%
        tick: 66750,
        liquidity: 270308022027705, // 更大的流动性
        sqrt_price_x96: 2815182732098351260075196715u128, // 价格1.0对应的sqrtPriceX96
        max_liquidity_per_tick: 11505743598341114571880798222544994,
        next_position_id: 1389,
    };
    
    // 计算准确的价格
    let price = PriceCalculator::sqrt_price_x96_to_price(
        metadata.sqrt_price_x96,
        metadata.token0.decimals,
        metadata.token1.decimals,
    ).unwrap_or(Decimal::from_f64(0.0001).unwrap());
    
    let inverse_price = if price > Decimal::ZERO {
        Decimal::ONE / price
    } else {
        Decimal::from(10000)
    };
    
    PoolState {
        metadata,
        price,
        inverse_price,
        last_updated: std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_secs(),
        canister_id: "test-pool-id".to_string(),
    }
}

#[tokio::test]
async fn test_swap_direction_accuracy() {
    println!("🔄 测试交易方向的准确性");

    let pool_state = create_test_pool_with_real_data();
    let amount_in = Decimal::from(1000);

    // 测试token0 -> token1
    let result_0_to_1 = PriceCalculator::calculate_amount_out_with_direction(amount_in, &pool_state, true);
    assert!(result_0_to_1.is_ok(), "token0->token1计算失败: {:?}", result_0_to_1);
    let amount_out_0_to_1 = result_0_to_1.unwrap();

    // 测试token1 -> token0
    let result_1_to_0 = PriceCalculator::calculate_amount_out_with_direction(amount_in, &pool_state, false);
    assert!(result_1_to_0.is_ok(), "token1->token0计算失败: {:?}", result_1_to_0);
    let amount_out_1_to_0 = result_1_to_0.unwrap();

    println!("交易方向测试:");
    println!("  Token0 -> Token1: {} -> {}", amount_in, amount_out_0_to_1);
    println!("  Token1 -> Token0: {} -> {}", amount_in, amount_out_1_to_0);

    // 验证两个方向的输出应该不同（除非价格正好是1.0）
    assert_ne!(amount_out_0_to_1, amount_out_1_to_0, "两个方向的输出不应该相同");

    // 验证输出都为正数
    assert!(amount_out_0_to_1 > Decimal::ZERO, "token0->token1输出应该大于0");
    assert!(amount_out_1_to_0 > Decimal::ZERO, "token1->token0输出应该大于0");
}

#[tokio::test]
async fn test_swap_by_tokens() {
    println!("🎯 测试根据代币地址确定交易方向");

    let pool_state = create_test_pool_with_real_data();
    let amount_in = Decimal::from(1000);

    let token0 = &pool_state.metadata.token0;
    let token1 = &pool_state.metadata.token1;

    // 测试token0 -> token1
    let result = PriceCalculator::calculate_amount_out_by_tokens(
        amount_in,
        &pool_state,
        token0,
        token1
    );
    assert!(result.is_ok(), "根据代币计算token0->token1失败: {:?}", result);
    let amount_out_0_to_1 = result.unwrap();

    // 测试token1 -> token0
    let result = PriceCalculator::calculate_amount_out_by_tokens(
        amount_in,
        &pool_state,
        token1,
        token0
    );
    assert!(result.is_ok(), "根据代币计算token1->token0失败: {:?}", result);
    let amount_out_1_to_0 = result.unwrap();

    println!("根据代币地址计算:");
    println!("  {} -> {}: {} -> {}", token0.symbol, token1.symbol, amount_in, amount_out_0_to_1);
    println!("  {} -> {}: {} -> {}", token1.symbol, token0.symbol, amount_in, amount_out_1_to_0);

    // 验证结果与直接指定方向的结果一致
    let direct_0_to_1 = PriceCalculator::calculate_amount_out_with_direction(amount_in, &pool_state, true).unwrap();
    let direct_1_to_0 = PriceCalculator::calculate_amount_out_with_direction(amount_in, &pool_state, false).unwrap();

    assert_eq!(amount_out_0_to_1, direct_0_to_1, "根据代币计算的结果应该与直接指定方向一致");
    assert_eq!(amount_out_1_to_0, direct_1_to_0, "根据代币计算的结果应该与直接指定方向一致");
}
