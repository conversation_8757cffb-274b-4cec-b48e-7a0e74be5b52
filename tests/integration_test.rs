use icpswap_arb::{
    config::Settings,
    data::{<PERSON>Manager, PriceCalculator},
    algorithms::{ArbitrageDetector, BellmanFord, TernarySearch},
    execution::RiskManager,
    monitoring::MetricsCollector,
    types::*,
};
use rust_decimal::Decimal;
use num_traits::{FromPrimitive, ToPrimitive};

#[tokio::test]
async fn test_pool_manager_initialization() {
    let settings = Settings::default();
    let pool_manager = PoolManager::new(settings);
    
    // 测试初始化
    let result = pool_manager.initialize().await;
    assert!(result.is_ok(), "池子管理器初始化失败: {:?}", result);
    
    // 测试获取池子统计
    let stats = pool_manager.get_stats().await;
    println!("池子统计: {:?}", stats);
    assert!(stats.total_pools > 0, "应该有池子数据");
}

#[tokio::test]
async fn test_price_calculator() {
    // 创建测试池子状态
    let pool_state = create_test_pool_state();
    
    // 测试价格计算
    let amount_in = Decimal::from(100);
    let result = PriceCalculator::calculate_amount_out(amount_in, &pool_state);
    assert!(result.is_ok(), "价格计算失败: {:?}", result);
    
    let amount_out = result.unwrap();
    println!("输入: {}, 输出: {}", amount_in, amount_out);
    assert!(amount_out > Decimal::ZERO, "输出金额应该大于0");
    
    // 测试滑点计算
    let slippage = PriceCalculator::calculate_slippage(amount_in, &pool_state);
    assert!(slippage.is_ok(), "滑点计算失败: {:?}", slippage);
    println!("滑点: {}", slippage.unwrap());
}

#[tokio::test]
async fn test_arbitrage_detection() {
    let settings = Settings::default();
    let pool_manager = PoolManager::new(settings.clone());
    pool_manager.initialize().await.expect("池子管理器初始化失败");
    
    let arbitrage_detector = ArbitrageDetector::new(settings.arbitrage, pool_manager);
    
    // 测试套利机会检测
    let opportunities = arbitrage_detector.detect_opportunities().await;
    assert!(opportunities.is_ok(), "套利检测失败: {:?}", opportunities);
    
    let opportunities = opportunities.unwrap();
    println!("检测到 {} 个套利机会", opportunities.len());
    
    for opportunity in &opportunities {
        println!("套利机会: ID={}, 利润={}, 置信度={}", 
                opportunity.id, opportunity.profit, opportunity.confidence);
    }
}

#[tokio::test]
async fn test_bellman_ford_algorithm() {
    let mut bf = BellmanFord::new();
    let graph = create_test_trading_graph();
    
    // 选择一个起始代币
    if let Some(start_token) = graph.nodes.first() {
        let result = bf.detect_arbitrage(&graph, &start_token.token);
        assert!(result.is_ok(), "Bellman-Ford算法执行失败: {:?}", result);
        
        let has_arbitrage = result.unwrap();
        println!("是否检测到套利机会: {}", has_arbitrage);
        
        if has_arbitrage {
            let cycle_path = bf.get_negative_cycle_path(&graph);
            if let Ok(path) = cycle_path {
                println!("套利路径: {:?}", path.iter().map(|t| &t.symbol).collect::<Vec<_>>());
            }
        }
    }
}

#[tokio::test]
async fn test_ternary_search() {
    // 测试三分搜索算法
    let profit_function = |x: Decimal| -> Result<Decimal> {
        // 模拟一个单峰函数: f(x) = -(x-5)^2 + 25
        let x_f64 = x.to_f64().unwrap_or(0.0);
        let result = -(x_f64 - 5.0).powi(2) + 25.0;
        Ok(Decimal::from_f64(result).unwrap_or(Decimal::ZERO))
    };
    
    let optimal = TernarySearch::find_optimal_amount(
        profit_function,
        Decimal::ZERO,
        Decimal::from(10),
        Decimal::from_f64(0.001).unwrap(),
        100,
    );
    
    assert!(optimal.is_ok(), "三分搜索失败: {:?}", optimal);
    let optimal_x = optimal.unwrap();
    println!("最优解: {}", optimal_x);
    
    // 验证结果接近5
    let diff = (optimal_x - Decimal::from(5)).abs();
    assert!(diff < Decimal::from_f64(0.1).unwrap(), "最优解应该接近5");
}

#[tokio::test]
async fn test_risk_manager() {
    let risk_config = Settings::default().risk;
    let risk_manager = RiskManager::new(risk_config);
    
    // 创建测试套利机会
    let opportunity = create_test_arbitrage_opportunity();
    
    // 测试风险评估
    let assessment = risk_manager.assess_risk(&opportunity).await;
    assert!(assessment.is_ok(), "风险评估失败: {:?}", assessment);
    
    let assessment = assessment.unwrap();
    println!("风险评估: 批准={}, 风险等级={:?}, 原因={}", 
            assessment.approved, assessment.risk_level, assessment.reason);
}

#[tokio::test]
async fn test_metrics_collector() {
    let metrics_collector = MetricsCollector::new();
    
    // 测试记录套利机会
    let opportunity = create_test_arbitrage_opportunity();
    metrics_collector.record_opportunity(&opportunity).await;
    
    // 测试记录交易结果
    let trade_result = ArbitrageResult {
        opportunity_id: opportunity.id.clone(),
        executed: true,
        actual_profit: Some(Decimal::from(50)),
        execution_time: 1234567890,
        gas_used: Some(100000),
        error: None,
    };
    metrics_collector.record_trade_result(&trade_result).await;
    
    // 获取统计信息
    let stats = metrics_collector.get_arbitrage_stats().await;
    println!("套利统计: {:?}", stats);
    assert_eq!(stats.total_opportunities, 1);
    assert_eq!(stats.executed_trades, 1);
}

// 辅助函数
fn create_test_pool_state() -> PoolState {
    let icp_token = Token {
        address: "rrkah-fqaaa-aaaaa-aaaaq-cai".to_string(),
        standard: "ICRC1".to_string(),
        symbol: "ICP".to_string(),
        decimals: 8,
    };
    
    let ckbtc_token = Token {
        address: "mxzaz-hqaaa-aaaar-qaada-cai".to_string(),
        standard: "ICRC1".to_string(),
        symbol: "ckBTC".to_string(),
        decimals: 8,
    };
    
    let metadata = PoolMetadata {
        key: "ICP-ckBTC-3000".to_string(),
        token0: icp_token,
        token1: ckbtc_token,
        fee: 3000,
        tick: 0,
        liquidity: 1000000000000000000,
        sqrt_price_x96: 79228162514264337593543950336,
        max_liquidity_per_tick: 11505743598341114571880798222544994,
        next_position_id: 1,
    };
    
    PoolState {
        metadata,
        price: Decimal::from_f64(0.0001).unwrap(),
        inverse_price: Decimal::from(10000),
        last_updated: std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_secs(),
        canister_id: "test-pool-id".to_string(),
    }
}

fn create_test_trading_graph() -> TradingGraph {
    let mut graph = TradingGraph::new();
    
    // 添加代币节点
    let icp = Token {
        address: "icp".to_string(),
        standard: "ICRC1".to_string(),
        symbol: "ICP".to_string(),
        decimals: 8,
    };
    
    let ckbtc = Token {
        address: "ckbtc".to_string(),
        standard: "ICRC1".to_string(),
        symbol: "ckBTC".to_string(),
        decimals: 8,
    };
    
    graph.add_node(icp.clone());
    graph.add_node(ckbtc.clone());
    
    // 添加边
    let edge1 = GraphEdge {
        from: icp.clone(),
        to: ckbtc.clone(),
        pool_id: "pool1".to_string(),
        weight: Decimal::from_f64(-0.1).unwrap(),
        fee: 3000,
        liquidity: 1000000000000000000,
    };
    
    let edge2 = GraphEdge {
        from: ckbtc,
        to: icp,
        pool_id: "pool2".to_string(),
        weight: Decimal::from_f64(-0.05).unwrap(),
        fee: 3000,
        liquidity: 1000000000000000000,
    };
    
    graph.add_edge(edge1);
    graph.add_edge(edge2);
    
    graph
}

fn create_test_arbitrage_opportunity() -> ArbitrageOpportunity {
    let icp = Token {
        address: "icp".to_string(),
        standard: "ICRC1".to_string(),
        symbol: "ICP".to_string(),
        decimals: 8,
    };
    
    let ckbtc = Token {
        address: "ckbtc".to_string(),
        standard: "ICRC1".to_string(),
        symbol: "ckBTC".to_string(),
        decimals: 8,
    };
    
    let path = TradePath {
        pools: vec!["pool1".to_string(), "pool2".to_string()],
        tokens: vec![icp, ckbtc],
        total_fee: 6000,
        expected_output: Decimal::from(110),
    };
    
    ArbitrageOpportunity {
        id: "test-opportunity".to_string(),
        path,
        input_amount: Decimal::from(100),
        expected_output: Decimal::from(110),
        profit: Decimal::from(10),
        profit_percentage: Decimal::from_f64(0.1).unwrap(),
        gas_cost: Decimal::from(1),
        net_profit: Decimal::from(9),
        confidence: 0.85,
        timestamp: 1234567890,
    }
}
