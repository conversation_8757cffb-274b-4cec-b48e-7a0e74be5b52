[package]
name = "icpswap-arb"
version = "0.1.0"
edition = "2021"

[dependencies]
tokio = { version = "1.0", features = ["full"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
anyhow = "1.0"
thiserror = "1.0"
log = "0.4"
env_logger = "0.10"
reqwest = { version = "0.11", features = ["json"] }
candid = "0.10"
ic-agent = "0.32"
ic-utils = "0.32"
num-bigint = "0.4"
num-traits = "0.2"
rust_decimal = "1.32"
dashmap = "5.5"
parking_lot = "0.12"
futures = "0.3"
async-trait = "0.1"
uuid = { version = "1.0", features = ["v4"] }

url = "2.4"
hex = "0.4"
tempfile = "3.8"

[dev-dependencies]
criterion = { version = "0.5", features = ["html_reports"] }

[[bench]]
name = "arbitrage_benchmarks"
harness = false
