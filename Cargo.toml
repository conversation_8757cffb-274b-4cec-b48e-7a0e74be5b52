[package]
name = "icpswap-arb"
version = "0.1.0"
edition = "2021"

[dependencies]
# 基础运行时和序列化
tokio = { version = "1.0", features = ["rt-multi-thread", "macros"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# 错误处理和日志
anyhow = "1.0"
log = "0.4"
env_logger = "0.10"

# 数学计算
num-bigint = "0.4"
num-traits = "0.2"

# UUID生成
uuid = { version = "1.0", features = ["v4"] }

[dev-dependencies]
# 移除复杂的基准测试
