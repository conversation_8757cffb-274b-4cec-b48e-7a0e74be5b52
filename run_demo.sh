#!/bin/bash

# ICP Swap 套利系统演示脚本

echo "🚀 ICP Swap 套利系统演示"
echo "=========================="

# 设置环境变量
export LOG_LEVEL=info
export MIN_PROFIT_THRESHOLD=5.0
export MAX_TRADE_AMOUNT=10000.0
export CONFIDENCE_THRESHOLD=0.7

echo "📋 配置信息:"
echo "  - 日志级别: $LOG_LEVEL"
echo "  - 最小利润阈值: $MIN_PROFIT_THRESHOLD"
echo "  - 最大交易金额: $MAX_TRADE_AMOUNT"
echo "  - 置信度阈值: $CONFIDENCE_THRESHOLD"
echo ""

echo "🔧 检查数据目录..."
if [ ! -d "data" ]; then
    echo "创建数据目录..."
    mkdir -p data
fi

echo "🧪 运行集成测试..."
cargo test --test integration_test --release

echo ""
echo "🎯 启动套利系统..."
echo "按 Ctrl+C 停止系统"
echo ""

# 运行主程序
timeout 30s ./target/release/icpswap-arb || echo "演示完成"

echo ""
echo "✅ 演示结束"
echo ""
echo "📊 系统功能总结:"
echo "  ✓ 池子数据管理 - 从JSON文件加载和管理交易池信息"
echo "  ✓ 价格计算 - 基于UniswapV3算法的价格计算"
echo "  ✓ 套利检测 - 使用Bellman-Ford算法检测套利机会"
echo "  ✓ 最优交易量 - 三分搜索算法优化交易量"
echo "  ✓ 风险管理 - 多层次风险评估和控制"
echo "  ✓ 监控日志 - 结构化日志和性能指标收集"
echo ""
echo "🔗 更多信息请查看 README.md"
