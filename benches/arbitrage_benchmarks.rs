/// ICPSwap V3套利系统性能基准测试
/// 
/// 运行命令: cargo bench
use criterion::{black_box, criterion_group, criterion_main, Criterion, BenchmarkId};
use icpswap_arb::{
    data::PriceCalculator,
    algorithms::{BellmanFord, TernarySearch},
    types::{PoolState, PoolMetadata, Token, ArbitrageOpportunity},
    simplified_arbitrage::{SimplifiedArbitrageSystem, ArbitrageConfig, PoolConfig},
};
use rust_decimal::Decimal;
use std::collections::HashMap;
use tokio::runtime::Runtime;

/// 基准测试：价格计算性能
fn benchmark_price_calculation(c: &mut Criterion) {
    let calculator = PriceCalculator::new();
    let pool_state = create_benchmark_pool_state();
    
    let mut group = c.benchmark_group("price_calculation");
    
    // 测试不同交易金额的计算性能
    for amount in [1000, 10000, 100000, 1000000, 10000000].iter() {
        group.bench_with_input(
            BenchmarkId::new("calculate_swap_output", amount),
            amount,
            |b, &amount| {
                b.iter(|| {
                    calculator.calculate_exact_swap_output(
                        black_box(Decimal::from(amount)),
                        black_box(&pool_state),
                        black_box(true),
                    )
                });
            },
        );
    }
    
    group.finish();
}

/// 基准测试：Bellman-Ford算法性能
fn benchmark_bellman_ford(c: &mut Criterion) {
    let bellman_ford = BellmanFord::new();
    
    let mut group = c.benchmark_group("bellman_ford");
    
    // 测试不同图大小的性能
    for size in [5, 10, 20, 50].iter() {
        let graph = create_test_graph(*size);
        
        group.bench_with_input(
            BenchmarkId::new("detect_negative_cycle", size),
            &graph,
            |b, graph| {
                b.iter(|| {
                    bellman_ford.detect_negative_cycle(black_box(graph))
                });
            },
        );
    }
    
    group.finish();
}

/// 基准测试：三分搜索算法性能
fn benchmark_ternary_search(c: &mut Criterion) {
    let ternary_search = TernarySearch::new();
    let opportunity = create_benchmark_opportunity();
    
    let mut group = c.benchmark_group("ternary_search");
    
    // 测试不同搜索范围的性能
    for range in [(1.0, 100.0), (1.0, 1000.0), (1.0, 10000.0)].iter() {
        group.bench_with_input(
            BenchmarkId::new("find_optimal_amount", format!("{}-{}", range.0, range.1)),
            range,
            |b, &(min, max)| {
                b.iter(|| {
                    ternary_search.find_optimal_amount(
                        black_box(&opportunity),
                        black_box(min),
                        black_box(max),
                    )
                });
            },
        );
    }
    
    group.finish();
}

/// 基准测试：系统初始化性能
fn benchmark_system_initialization(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();
    
    c.bench_function("system_initialization", |b| {
        b.iter(|| {
            rt.block_on(async {
                let config = create_benchmark_config();
                let system = SimplifiedArbitrageSystem::new(black_box(config));
                
                // 测试系统启动时间
                system.start().await.expect("系统启动失败");
                
                // 等待初始化完成
                tokio::time::sleep(tokio::time::Duration::from_millis(10)).await;
                
                // 验证系统状态
                let _stats = system.get_stats().await;
            });
        });
    });
}

/// 基准测试：并发数据访问性能
fn benchmark_concurrent_access(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();
    
    let mut group = c.benchmark_group("concurrent_access");
    
    for thread_count in [1, 2, 4, 8].iter() {
        group.bench_with_input(
            BenchmarkId::new("concurrent_pool_access", thread_count),
            thread_count,
            |b, &thread_count| {
                b.iter(|| {
                    rt.block_on(async {
                        let config = create_benchmark_config();
                        let system = std::sync::Arc::new(
                            SimplifiedArbitrageSystem::new(config)
                        );
                        
                        system.start().await.expect("系统启动失败");
                        tokio::time::sleep(tokio::time::Duration::from_millis(10)).await;
                        
                        let mut handles = Vec::new();
                        
                        for _ in 0..thread_count {
                            let system_clone = system.clone();
                            let handle = tokio::spawn(async move {
                                for _ in 0..10 {
                                    let _pools = system_clone.get_pools().await;
                                    let _stats = system_clone.get_stats().await;
                                }
                            });
                            handles.push(handle);
                        }
                        
                        for handle in handles {
                            handle.await.expect("任务完成失败");
                        }
                    });
                });
            },
        );
    }
    
    group.finish();
}

/// 基准测试：内存使用效率
fn benchmark_memory_efficiency(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();
    
    c.bench_function("memory_efficiency", |b| {
        b.iter(|| {
            rt.block_on(async {
                let config = create_benchmark_config();
                let system = SimplifiedArbitrageSystem::new(black_box(config));
                
                system.start().await.expect("系统启动失败");
                
                // 模拟长时间运行
                for _ in 0..100 {
                    let _pools = system.get_pools().await;
                    let _stats = system.get_stats().await;
                }
            });
        });
    });
}

/// 创建基准测试用的池子状态
fn create_benchmark_pool_state() -> PoolState {
    PoolState {
        metadata: PoolMetadata {
            key: "benchmark-pool".to_string(),
            token0: Token {
                symbol: "ICP".to_string(),
                decimals: 8,
                principal: None,
            },
            token1: Token {
                symbol: "ckBTC".to_string(),
                decimals: 8,
                principal: None,
            },
            fee: 3000,
            tick: 0,
            liquidity: 81919571592,
            sqrt_price_x96: 11459044431076952053073673485164u128,
            max_liquidity_per_tick: 1000000000000000000u128,
            next_position_id: 1,
        },
        price: Decimal::from_str("20918.84").unwrap(),
        inverse_price: Decimal::from_str("0.0000478").unwrap(),
        last_updated: 1234567890,
        canister_id: "benchmark-canister".to_string(),
    }
}

/// 创建基准测试用的图
fn create_test_graph(size: usize) -> HashMap<String, Vec<(String, f64)>> {
    let mut graph = HashMap::new();
    
    for i in 0..size {
        let node = format!("Node{}", i);
        let mut edges = Vec::new();
        
        // 创建到其他节点的边
        for j in 0..size {
            if i != j {
                let target = format!("Node{}", j);
                let weight = (i as f64 - j as f64) * 0.01; // 模拟权重
                edges.push((target, weight));
            }
        }
        
        graph.insert(node, edges);
    }
    
    graph
}

/// 创建基准测试用的套利机会
fn create_benchmark_opportunity() -> ArbitrageOpportunity {
    ArbitrageOpportunity {
        path: vec!["ICP".to_string(), "ckBTC".to_string(), "ckETH".to_string(), "ICP".to_string()],
        amounts: vec![100.0, 95.0, 98.0, 105.0],
        profit_percentage: 0.05,
        estimated_gas: 1000000,
        confidence: 0.9,
    }
}

/// 创建基准测试用的配置
fn create_benchmark_config() -> ArbitrageConfig {
    ArbitrageConfig {
        min_profit_threshold: 0.005,
        max_trade_amount: 1000.0,
        update_interval_secs: 1,
        enabled_pools: vec![
            PoolConfig {
                id: "BENCH-ICP-ckBTC".to_string(),
                canister_id: "bench-canister-1".to_string(),
                token0_symbol: "ICP".to_string(),
                token1_symbol: "ckBTC".to_string(),
                enabled: true,
            },
            PoolConfig {
                id: "BENCH-ICP-ckETH".to_string(),
                canister_id: "bench-canister-2".to_string(),
                token0_symbol: "ICP".to_string(),
                token1_symbol: "ckETH".to_string(),
                enabled: true,
            },
            PoolConfig {
                id: "BENCH-ckBTC-ckETH".to_string(),
                canister_id: "bench-canister-3".to_string(),
                token0_symbol: "ckBTC".to_string(),
                token1_symbol: "ckETH".to_string(),
                enabled: true,
            },
        ],
    }
}

// 注册基准测试
criterion_group!(
    benches,
    benchmark_price_calculation,
    benchmark_bellman_ford,
    benchmark_ternary_search,
    benchmark_system_initialization,
    benchmark_concurrent_access,
    benchmark_memory_efficiency
);

criterion_main!(benches);
