/// 简化的ICPSwap V3套利系统演示
/// 
/// 这个示例展示了一个功能完整但简化的套利系统，
/// 包含实时数据更新、套利检测和监控功能
use icpswap_arb::simplified_arbitrage::{
    SimplifiedArbitrageSystem, ArbitrageConfig, PoolConfig, SystemStats
};
use log::{info, error};
use std::time::Duration;
use tokio::time::interval;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 初始化日志
    env_logger::Builder::from_default_env()
        .filter_level(log::LevelFilter::Info)
        .init();
    
    info!("🚀 启动ICPSwap V3简化套利系统演示");
    
    // 创建配置
    let config = ArbitrageConfig {
        min_profit_threshold: 0.005, // 0.5%最小利润
        max_trade_amount: 100.0,
        update_interval_secs: 5, // 5秒更新间隔
        enabled_pools: vec![
            PoolConfig {
                id: "ICP-ckBTC-3000".to_string(),
                canister_id: "rdmx6-jaaaa-aaaah-qcaiq-cai".to_string(),
                token0_symbol: "ICP".to_string(),
                token1_symbol: "ckBTC".to_string(),
                enabled: true,
            },
            PoolConfig {
                id: "ICP-ckETH-3000".to_string(),
                canister_id: "xkbqi-6qaaa-aaaah-qcaiq-cai".to_string(),
                token0_symbol: "ICP".to_string(),
                token1_symbol: "ckETH".to_string(),
                enabled: true,
            },
            PoolConfig {
                id: "ckBTC-ckETH-500".to_string(),
                canister_id: "5hr3g-hqaaa-aaaah-qcaiq-cai".to_string(),
                token0_symbol: "ckBTC".to_string(),
                token1_symbol: "ckETH".to_string(),
                enabled: true,
            },
            PoolConfig {
                id: "ICP-ckUSDC-3000".to_string(),
                canister_id: "ggzvv-5qaaa-aaaah-qcaiq-cai".to_string(),
                token0_symbol: "ICP".to_string(),
                token1_symbol: "ckUSDC".to_string(),
                enabled: true,
            },
        ],
    };
    
    // 创建套利系统
    let arbitrage_system = SimplifiedArbitrageSystem::new(config);
    
    // 启动系统
    arbitrage_system.start().await?;
    
    // 启动监控循环
    let system_clone = arbitrage_system.clone_for_async();
    tokio::spawn(async move {
        let mut interval = interval(Duration::from_secs(30)); // 30秒显示一次统计
        
        loop {
            interval.tick().await;
            display_system_status(&system_clone).await;
        }
    });
    
    info!("✅ 系统启动完成");
    info!("💡 系统将持续监控套利机会...");
    info!("💡 按 Ctrl+C 停止系统");
    
    // 主循环
    loop {
        tokio::select! {
            _ = tokio::signal::ctrl_c() => {
                info!("🛑 收到停止信号，正在关闭系统...");
                break;
            }
            _ = tokio::time::sleep(Duration::from_secs(1)) => {
                // 保持主线程运行
            }
        }
    }
    
    info!("✅ 系统已安全关闭");
    Ok(())
}

/// 显示系统状态
async fn display_system_status(system: &SimplifiedArbitrageSystem) {
    match system.get_stats().await {
        stats => {
            info!("📊 ===== 系统状态报告 =====");
            info!("🏊 池子统计:");
            info!("  - 总池子数: {}", stats.total_pools);
            info!("  - 活跃池子: {}", stats.active_pools);
            info!("  - 总流动性: {:.2}", stats.total_liquidity);
            info!("  - 平均价格影响: {:.2}%", stats.avg_price_impact * 100.0);
            
            // 显示池子详情
            let pools = system.get_pools().await;
            info!("📋 池子详情:");
            for (id, pool) in pools.iter() {
                let price = if pool.sqrt_price_x96 > 0 {
                    (pool.sqrt_price_x96 as f64) / (1u128 << 96) as f64
                } else {
                    0.0
                };
                
                info!("  - {}: {} -> {} | 价格: {:.6} | 流动性: {}", 
                      id, 
                      pool.token0_symbol, 
                      pool.token1_symbol,
                      price,
                      pool.liquidity);
            }
            
            info!("📊 ========================");
        }
    }
}

/// 为SimplifiedArbitrageSystem实现clone_for_async方法
impl SimplifiedArbitrageSystem {
    pub fn clone_for_async(&self) -> Self {
        Self {
            pools: self.pools.clone(),
            price_calculator: self.price_calculator.clone(),
            bellman_ford: self.bellman_ford.clone(),
            ternary_search: self.ternary_search.clone(),
            config: self.config.clone(),
        }
    }
}
