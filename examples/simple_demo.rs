/// 简化的ICPSwap V3套利系统演示
/// 
/// 这个示例展示了一个功能完整但简化的套利系统，
/// 可以编译运行并展示核心功能
use icpswap_arb::simple_system::{SimpleArbitrageSystem, SystemConfig, PoolConfig};
use log::info;
use std::time::Duration;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 初始化日志
    env_logger::Builder::from_default_env()
        .filter_level(log::LevelFilter::Info)
        .init();
    
    info!("🚀 启动ICPSwap V3简化套利系统演示");
    
    // 创建系统配置
    let config = SystemConfig {
        min_profit_threshold: 0.002, // 0.2%最小利润
        max_trade_amount: 500.0,
        update_interval_secs: 3, // 3秒更新间隔
        pools: vec![
            PoolConfig {
                id: "ICP-ckBTC-3000".to_string(),
                token0_symbol: "ICP".to_string(),
                token1_symbol: "ckBTC".to_string(),
                enabled: true,
            },
            PoolConfig {
                id: "ICP-ckETH-3000".to_string(),
                token0_symbol: "ICP".to_string(),
                token1_symbol: "ckETH".to_string(),
                enabled: true,
            },
            PoolConfig {
                id: "ckBTC-ckETH-500".to_string(),
                token0_symbol: "ckBTC".to_string(),
                token1_symbol: "ckETH".to_string(),
                enabled: true,
            },
            PoolConfig {
                id: "ICP-ckUSDC-3000".to_string(),
                token0_symbol: "ICP".to_string(),
                token1_symbol: "ckUSDC".to_string(),
                enabled: true,
            },
            PoolConfig {
                id: "ckBTC-ckUSDC-500".to_string(),
                token0_symbol: "ckBTC".to_string(),
                token1_symbol: "ckUSDC".to_string(),
                enabled: true,
            },
        ],
    };
    
    // 创建套利系统
    let mut arbitrage_system = SimpleArbitrageSystem::new(config);
    
    info!("✅ 系统配置完成");
    info!("💡 系统将模拟ICPSwap V3池子数据并检测套利机会");
    info!("💡 按 Ctrl+C 停止系统");
    
    // 启动系统（这会运行主循环）
    match arbitrage_system.start().await {
        Ok(_) => {
            info!("✅ 系统正常退出");
        }
        Err(e) => {
            eprintln!("❌ 系统运行错误: {}", e);
        }
    }
    
    Ok(())
}

/// 运行有限时间的演示
#[allow(dead_code)]
async fn run_limited_demo() -> Result<(), Box<dyn std::error::Error>> {
    // 初始化日志
    env_logger::Builder::from_default_env()
        .filter_level(log::LevelFilter::Info)
        .init();
    
    info!("🚀 启动限时演示 (30秒)");
    
    // 创建系统配置
    let config = SystemConfig {
        min_profit_threshold: 0.001, // 0.1%最小利润（更容易触发）
        max_trade_amount: 1000.0,
        update_interval_secs: 2, // 2秒更新间隔
        pools: vec![
            PoolConfig {
                id: "DEMO-ICP-ckBTC".to_string(),
                token0_symbol: "ICP".to_string(),
                token1_symbol: "ckBTC".to_string(),
                enabled: true,
            },
            PoolConfig {
                id: "DEMO-ICP-ckETH".to_string(),
                token0_symbol: "ICP".to_string(),
                token1_symbol: "ckETH".to_string(),
                enabled: true,
            },
            PoolConfig {
                id: "DEMO-ckBTC-ckETH".to_string(),
                token0_symbol: "ckBTC".to_string(),
                token1_symbol: "ckETH".to_string(),
                enabled: true,
            },
        ],
    };
    
    // 创建套利系统
    let mut arbitrage_system = SimpleArbitrageSystem::new(config);
    
    // 初始化池子
    arbitrage_system.initialize_pools().await?;
    
    info!("📊 初始池子状态:");
    for (id, pool) in arbitrage_system.get_pools() {
        info!("  - {}: {} -> {} | 价格: {:.6} | 流动性: {:.0}", 
              id, pool.token0_symbol, pool.token1_symbol, pool.price, pool.liquidity);
    }
    
    // 运行30秒
    let start_time = std::time::Instant::now();
    let mut cycle_count = 0;
    
    while start_time.elapsed() < Duration::from_secs(30) {
        // 更新数据
        arbitrage_system.update_pool_data().await?;
        
        // 检测套利机会
        arbitrage_system.detect_arbitrage_opportunities().await?;
        
        cycle_count += 1;
        
        // 每5个周期显示一次详细信息
        if cycle_count % 5 == 0 {
            arbitrage_system.display_stats().await;
            
            let opportunities = arbitrage_system.get_opportunities();
            if !opportunities.is_empty() {
                info!("🎯 当前最佳套利机会:");
                for (i, opp) in opportunities.iter().take(3).enumerate() {
                    info!("  {}. 路径: {} | 利润: {:.4}% | 预估收益: ${:.2}", 
                          i + 1,
                          opp.path.join(" -> "),
                          opp.profit_percentage * 100.0,
                          opp.estimated_profit);
                }
            }
        }
        
        // 等待2秒
        tokio::time::sleep(Duration::from_secs(2)).await;
    }
    
    info!("⏰ 演示结束 - 总共运行了 {} 个更新周期", cycle_count);
    
    // 最终统计
    info!("📈 最终统计:");
    arbitrage_system.display_stats().await;
    
    let final_opportunities = arbitrage_system.get_opportunities();
    info!("🏆 发现的套利机会总数: {}", final_opportunities.len());
    
    if !final_opportunities.is_empty() {
        let best_opportunity = final_opportunities.iter()
            .max_by(|a, b| a.profit_percentage.partial_cmp(&b.profit_percentage).unwrap())
            .unwrap();
        
        info!("🥇 最佳套利机会:");
        info!("   路径: {}", best_opportunity.path.join(" -> "));
        info!("   利润率: {:.4}%", best_opportunity.profit_percentage * 100.0);
        info!("   预估收益: ${:.2}", best_opportunity.estimated_profit);
        info!("   置信度: {:.2}", best_opportunity.confidence);
    }
    
    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[tokio::test]
    async fn test_demo_system() {
        let config = SystemConfig::default();
        let mut system = SimpleArbitrageSystem::new(config);
        
        // 测试初始化
        let result = system.initialize_pools().await;
        assert!(result.is_ok());
        
        // 测试数据更新
        let result = system.update_pool_data().await;
        assert!(result.is_ok());
        
        // 测试套利检测
        let result = system.detect_arbitrage_opportunities().await;
        assert!(result.is_ok());
    }
}
