use icpswap_arb::{
    network::{ICPClient, ICPConfig, DataFetcher, CacheManager, CacheConfig, RealtimeUpdater, UpdaterConfig},
    core::{ArbitrageEngine, ArbitrageEngineConfig},
    config::{PoolConfigManager},
    execution::{ICPSwapExecutor},
    types::Result,
};
use candid::Principal;
use std::collections::HashMap;
use std::time::Duration;
use log::{info, error, warn};
use std::sync::Arc;

/// 实时套利系统示例
/// 
/// 这个示例展示了如何：
/// 1. 连接到ICP网络
/// 2. 配置数据获取器和缓存
/// 3. 启动实时数据更新
/// 4. 集成套利检测和执行
#[tokio::main]
async fn main() -> Result<()> {
    // 初始化日志
    env_logger::init();
    
    info!("🚀 启动ICPSwap V3实时套利系统");
    
    // 1. 配置ICP网络连接
    let icp_config = ICPConfig {
        network_url: "https://ic0.app".to_string(), // 主网
        use_mainnet: true,
        timeout_secs: 30,
        max_retries: 3,
    };
    
    // 2. 创建ICP客户端
    info!("📡 连接到ICP网络...");
    let icp_client = ICPClient::new(icp_config).await?;
    
    // 检查网络连接
    if !icp_client.check_connection().await? {
        error!("❌ ICP网络连接失败");
        return Ok(());
    }
    info!("✅ ICP网络连接成功");
    
    // 3. 加载池子配置
    info!("📋 加载池子配置...");
    let pool_config = PoolConfigManager::load_from_file("config/pools.json")?;

    // 验证配置
    pool_config.validate_config()?;
    let stats = pool_config.get_stats();
    info!("✅ {}", stats);

    // 4. 配置数据获取器
    let mut data_fetcher = DataFetcher::new(icp_client.clone());

    // 添加池子Canister映射
    let pool_canisters = pool_config.get_pool_canister_mapping()?;
    data_fetcher.add_pool_canisters(pool_canisters);
    
    // 5. 配置缓存管理器
    let cache_config = CacheConfig {
        ttl: Duration::from_secs(30), // 30秒缓存
        max_entries: 1000,
        cleanup_interval: Duration::from_secs(60),
        enable_lru: true,
    };

    // 6. 配置实时更新器
    let updater_config = UpdaterConfig {
        update_interval: Duration::from_secs(10), // 10秒更新间隔（避免过于频繁）
        batch_size: 5,
        enable_change_detection: true,
        max_concurrent_updates: 3,
        max_retries: 3,
        retry_interval: Duration::from_secs(2),
    };

    // 7. 创建实时更新器
    info!("⚙️ 初始化实时数据更新器...");
    let mut realtime_updater = RealtimeUpdater::new(
        data_fetcher,
        cache_config,
        updater_config,
    );

    // 添加要监控的池子（从配置文件获取）
    let enabled_pools = pool_config.get_enabled_pools();
    for pool in &enabled_pools {
        realtime_updater.add_monitored_pool(pool.id.clone()).await;
        info!("📊 添加监控池子: {}", pool.id);
    }

    // 8. 创建交易执行器
    info!("🔧 初始化交易执行器...");
    let user_principal = Principal::from_text("rdmx6-jaaaa-aaaah-qcaiq-cai")
        .map_err(|e| anyhow::anyhow!("无效的用户Principal: {}", e))?;
    let icpswap_executor = ICPSwapExecutor::new(icp_client.clone(), user_principal);
    
    // 9. 配置套利引擎
    let arbitrage_config = ArbitrageEngineConfig {
        min_profit_threshold: 0.005, // 0.5%最小利润（更现实的阈值）
        max_trade_amount: 100.0, // 降低最大交易金额以控制风险
        risk_tolerance: 0.02, // 2%风险容忍度
        enable_auto_execution: false, // 演示模式，不自动执行
    };

    // 10. 创建套利引擎
    info!("🧠 初始化套利引擎...");
    let mut arbitrage_engine = ArbitrageEngine::new(arbitrage_config);
    
    // 将实时更新器与套利引擎集成
    let updater_arc = Arc::new(realtime_updater);
    arbitrage_engine.set_realtime_updater(updater_arc.clone());
    
    // 11. 启动系统
    info!("🔄 启动实时数据更新...");
    let updater_arc = Arc::new(realtime_updater);
    arbitrage_engine.set_realtime_updater(updater_arc.clone());

    // 启动更新器
    let mut updater_mut = Arc::try_unwrap(updater_arc)
        .map_err(|_| anyhow::anyhow!("无法获取updater的可变引用"))?;
    updater_mut.start().await?;

    info!("🎯 启动套利引擎...");
    arbitrage_engine.start().await?;

    // 12. 订阅更新事件
    let mut event_receiver = updater_mut.subscribe();

    // 13. 手动触发一次初始数据更新
    info!("🔄 触发初始数据更新...");
    if let Err(e) = updater_mut.trigger_update().await {
        warn!("初始数据更新失败: {}", e);
    }
    
    info!("✅ 系统启动完成，开始监控套利机会...");
    info!("💡 提示: 按 Ctrl+C 停止系统");

    // 14. 主循环 - 监听事件和显示统计信息
    let mut stats_interval = tokio::time::interval(Duration::from_secs(60)); // 1分钟显示一次统计
    let mut health_check_interval = tokio::time::interval(Duration::from_secs(300)); // 5分钟健康检查
    
    loop {
        tokio::select! {
            // 处理更新事件
            event = event_receiver.recv() => {
                match event {
                    Ok(update_event) => {
                        handle_update_event(update_event).await;
                    }
                    Err(e) => {
                        error!("接收更新事件失败: {}", e);
                    }
                }
            }
            
            // 定期显示统计信息
            _ = stats_interval.tick() => {
                display_system_stats(&updater_mut, &arbitrage_engine, &pool_config).await;
            }

            // 定期健康检查
            _ = health_check_interval.tick() => {
                perform_health_check(&updater_mut, &icp_client).await;
            }
            
            // 处理Ctrl+C信号
            _ = tokio::signal::ctrl_c() => {
                info!("🛑 收到停止信号，正在关闭系统...");
                break;
            }
        }
    }
    
    // 15. 优雅关闭
    info!("🔄 正在停止系统组件...");

    info!("🔄 停止套利引擎...");
    if let Err(e) = arbitrage_engine.stop().await {
        error!("停止套利引擎失败: {}", e);
    }

    info!("🔄 停止实时数据更新器...");
    updater_mut.stop().await;

    info!("✅ 系统已安全关闭");
    Ok(())
}

/// 处理更新事件
async fn handle_update_event(event: icpswap_arb::network::UpdateEvent) {
    use icpswap_arb::network::UpdateEvent;
    
    match event {
        UpdateEvent::PoolUpdated { pool_id, .. } => {
            info!("📊 池子数据更新: {}", pool_id);
        }
        UpdateEvent::BatchPoolsUpdated { updated_pools } => {
            info!("📊 批量池子更新: {} 个池子", updated_pools.len());
        }
        UpdateEvent::UpdateError { pool_id, error } => {
            error!("❌ 池子更新错误 {}: {}", pool_id, error);
        }
        UpdateEvent::ArbitrageOpportunity { opportunity } => {
            info!("💰 发现套利机会: {}", opportunity);
        }
    }
}

/// 显示系统统计信息
async fn display_system_stats(
    updater: &RealtimeUpdater,
    engine: &ArbitrageEngine,
    pool_config: &PoolConfigManager,
) {
    info!("📊 ===== 系统状态报告 =====");

    // 缓存统计
    let cache_stats = updater.get_cache_stats();
    info!("📈 缓存统计 - 命中率: {:.2}%, 条目数: {}, 命中: {}, 未命中: {}",
          cache_stats.hit_rate() * 100.0,
          cache_stats.entries_count,
          cache_stats.hits,
          cache_stats.misses);

    // 套利机会统计
    let opportunities = engine.get_active_opportunities().await;
    if opportunities.is_empty() {
        info!("🎯 当前无活跃套利机会");
    } else {
        info!("🎯 当前活跃套利机会: {} 个", opportunities.len());
        for (i, opp) in opportunities.iter().take(3).enumerate() {
            info!("   {}. 利润: {:.4}%", i + 1, opp.profit_percentage * 100.0);
        }
    }

    // 池子状态统计
    let pools = engine.get_current_pools().await;
    let config_stats = pool_config.get_stats();
    info!("🏊 池子状态 - 配置: {}, 监控: {}, 缓存: {}",
          config_stats.enabled_pools,
          pools.len(),
          cache_stats.entries_count);

    info!("📊 ========================");
}

/// 执行健康检查
async fn perform_health_check(
    updater: &RealtimeUpdater,
    icp_client: &ICPClient,
) {
    info!("🔍 执行系统健康检查...");

    // 检查ICP网络连接
    match icp_client.check_connection().await {
        Ok(true) => info!("✅ ICP网络连接正常"),
        Ok(false) => warn!("⚠️ ICP网络连接异常"),
        Err(e) => error!("❌ ICP网络检查失败: {}", e),
    }

    // 检查缓存状态
    let cache_stats = updater.get_cache_stats();
    if cache_stats.entries_count == 0 {
        warn!("⚠️ 缓存为空，可能存在数据获取问题");
    }

    if cache_stats.hit_rate() < 0.5 {
        warn!("⚠️ 缓存命中率较低: {:.2}%", cache_stats.hit_rate() * 100.0);
    }
}


