use log::{info, warn, error, debug};
use serde_json::json;
use std::collections::HashMap;
use tokio::sync::RwLock;
use std::sync::Arc;

use crate::types::{ArbitrageOpportunity, ArbitrageResult, Result, ArbitrageError};
use crate::config::MonitoringConfig;

/// 结构化日志记录器
pub struct StructuredLogger {
    config: MonitoringConfig,
    /// 性能指标缓存
    metrics_cache: Arc<RwLock<HashMap<String, serde_json::Value>>>,
}

impl StructuredLogger {
    /// 创建新的结构化日志记录器
    pub fn new(config: MonitoringConfig) -> Self {
        Self {
            config,
            metrics_cache: Arc::new(RwLock::new(HashMap::new())),
        }
    }
    
    /// 初始化日志系统
    pub fn initialize(&self) -> Result<()> {
        env_logger::Builder::from_env(
            env_logger::Env::default().default_filter_or(&self.config.log_level)
        )
        .format_timestamp_secs()
        .init();
        
        info!("日志系统初始化完成，日志级别: {}", self.config.log_level);
        Ok(())
    }
    
    /// 记录套利机会发现
    pub async fn log_opportunity_detected(&self, opportunity: &ArbitrageOpportunity) {
        let log_data = json!({
            "event": "opportunity_detected",
            "opportunity_id": opportunity.id,
            "timestamp": opportunity.timestamp,
            "path": {
                "tokens": opportunity.path.tokens.iter().map(|t| &t.symbol).collect::<Vec<_>>(),
                "pools": opportunity.path.pools,
                "total_fee": opportunity.path.total_fee
            },
            "metrics": {
                "input_amount": opportunity.input_amount.to_string(),
                "expected_output": opportunity.expected_output.to_string(),
                "profit": opportunity.profit.to_string(),
                "profit_percentage": opportunity.profit_percentage.to_string(),
                "net_profit": opportunity.net_profit.to_string(),
                "confidence": opportunity.confidence,
                "gas_cost": opportunity.gas_cost.to_string()
            }
        });
        
        info!("套利机会检测: {}", log_data);
        
        // 缓存指标
        self.cache_metric("last_opportunity", log_data).await;
    }
    
    /// 记录交易执行开始
    pub async fn log_trade_execution_start(&self, opportunity: &ArbitrageOpportunity) {
        let log_data = json!({
            "event": "trade_execution_start",
            "opportunity_id": opportunity.id,
            "timestamp": std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap()
                .as_secs(),
            "input_amount": opportunity.input_amount.to_string(),
            "expected_profit": opportunity.profit.to_string(),
            "path_length": opportunity.path.pools.len()
        });
        
        info!("开始执行交易: {}", log_data);
    }
    
    /// 记录交易执行结果
    pub async fn log_trade_execution_result(&self, result: &ArbitrageResult) {
        let log_data = json!({
            "event": "trade_execution_result",
            "opportunity_id": result.opportunity_id,
            "timestamp": result.execution_time,
            "executed": result.executed,
            "actual_profit": result.actual_profit.map(|p| p.to_string()),
            "gas_used": result.gas_used,
            "error": result.error
        });
        
        if result.executed {
            if let Some(profit) = result.actual_profit {
                if profit > rust_decimal::Decimal::ZERO {
                    info!("交易执行成功: {}", log_data);
                } else {
                    warn!("交易执行亏损: {}", log_data);
                }
            } else {
                info!("交易执行完成: {}", log_data);
            }
        } else {
            error!("交易执行失败: {}", log_data);
        }
        
        // 缓存指标
        self.cache_metric("last_trade_result", log_data).await;
    }
    
    /// 记录风险管理事件
    pub async fn log_risk_event(&self, event_type: &str, details: serde_json::Value) {
        let log_data = json!({
            "event": "risk_management",
            "event_type": event_type,
            "timestamp": std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap()
                .as_secs(),
            "details": details
        });
        
        match event_type {
            "emergency_stop" | "daily_loss_limit" => {
                error!("风险管理事件: {}", log_data);
            },
            "position_limit" | "confidence_low" => {
                warn!("风险管理事件: {}", log_data);
            },
            _ => {
                info!("风险管理事件: {}", log_data);
            }
        }
        
        // 缓存指标
        self.cache_metric("last_risk_event", log_data).await;
    }
    
    /// 记录系统性能指标
    pub async fn log_performance_metrics(&self, metrics: &PerformanceMetrics) {
        let log_data = json!({
            "event": "performance_metrics",
            "timestamp": std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap()
                .as_secs(),
            "metrics": {
                "total_opportunities": metrics.total_opportunities,
                "executed_trades": metrics.executed_trades,
                "success_rate": metrics.success_rate,
                "total_profit": metrics.total_profit.to_string(),
                "average_profit": metrics.average_profit.to_string(),
                "active_pools": metrics.active_pools,
                "detection_latency_ms": metrics.detection_latency_ms,
                "execution_latency_ms": metrics.execution_latency_ms,
                "memory_usage_mb": metrics.memory_usage_mb,
                "cpu_usage_percent": metrics.cpu_usage_percent
            }
        });
        
        info!("性能指标: {}", log_data);
        
        // 缓存指标
        self.cache_metric("performance_metrics", log_data).await;
    }
    
    /// 记录错误事件
    pub async fn log_error(&self, error: &ArbitrageError, context: Option<&str>) {
        let log_data = json!({
            "event": "error",
            "timestamp": std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap()
                .as_secs(),
            "error_type": self.get_error_type(error),
            "error_message": error.to_string(),
            "context": context
        });
        
        error!("系统错误: {}", log_data);
        
        // 发送告警（如果配置了webhook）
        if let Some(webhook_url) = &self.config.alert_webhook_url {
            self.send_alert(webhook_url, &log_data).await;
        }

        // 缓存指标
        self.cache_metric("last_error", log_data).await;
    }
    
    /// 记录系统启动事件
    pub async fn log_system_startup(&self, config_summary: serde_json::Value) {
        let log_data = json!({
            "event": "system_startup",
            "timestamp": std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap()
                .as_secs(),
            "config": config_summary,
            "version": env!("CARGO_PKG_VERSION")
        });
        
        info!("系统启动: {}", log_data);
    }
    
    /// 记录系统关闭事件
    pub async fn log_system_shutdown(&self, reason: &str) {
        let log_data = json!({
            "event": "system_shutdown",
            "timestamp": std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap()
                .as_secs(),
            "reason": reason
        });
        
        info!("系统关闭: {}", log_data);
    }
    
    /// 缓存指标
    async fn cache_metric(&self, key: &str, value: serde_json::Value) {
        let mut cache = self.metrics_cache.write().await;
        cache.insert(key.to_string(), value);
    }
    
    /// 获取缓存的指标
    pub async fn get_cached_metrics(&self) -> HashMap<String, serde_json::Value> {
        self.metrics_cache.read().await.clone()
    }
    
    /// 获取错误类型
    fn get_error_type(&self, error: &ArbitrageError) -> &'static str {
        match error {
            ArbitrageError::DataLoadError(_) => "data_load_error",
            ArbitrageError::PriceCalculationError(_) => "price_calculation_error",
            ArbitrageError::ArbitrageDetectionError(_) => "arbitrage_detection_error",
            ArbitrageError::TradeExecutionError(_) => "trade_execution_error",
            ArbitrageError::NetworkError(_) => "network_error",
            ArbitrageError::SerializationError(_) => "serialization_error",
            ArbitrageError::IoError(_) => "io_error",
            ArbitrageError::ConfigError(_) => "config_error",
            ArbitrageError::RiskManagementError(_) => "risk_management_error",
            ArbitrageError::InternalError(_) => "internal_error",
        }
    }
    
    /// 发送告警
    async fn send_alert(&self, webhook_url: &str, alert_data: &serde_json::Value) {
        let client = reqwest::Client::new();
        
        match client
            .post(webhook_url)
            .json(alert_data)
            .send()
            .await
        {
            Ok(response) => {
                if response.status().is_success() {
                    debug!("告警发送成功");
                } else {
                    warn!("告警发送失败，状态码: {}", response.status());
                }
            },
            Err(e) => {
                warn!("告警发送失败: {}", e);
            }
        }
    }
}

/// 性能指标结构
#[derive(Debug, Clone)]
pub struct PerformanceMetrics {
    pub total_opportunities: u64,
    pub executed_trades: u64,
    pub success_rate: f64,
    pub total_profit: rust_decimal::Decimal,
    pub average_profit: rust_decimal::Decimal,
    pub active_pools: usize,
    pub detection_latency_ms: u64,
    pub execution_latency_ms: u64,
    pub memory_usage_mb: u64,
    pub cpu_usage_percent: f64,
}
