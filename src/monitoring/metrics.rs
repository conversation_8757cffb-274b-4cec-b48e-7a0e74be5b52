use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;
use tokio::time::{Duration, interval};
use rust_decimal::Decimal;
use num_traits::ToPrimitive;

use crate::types::{ArbitrageOpportunity, ArbitrageResult, ArbitrageStats};
use crate::monitoring::PerformanceMetrics;

/// 指标收集器
pub struct MetricsCollector {
    /// 套利统计
    arbitrage_stats: Arc<RwLock<ArbitrageStats>>,
    /// 性能指标
    performance_metrics: Arc<RwLock<PerformanceMetrics>>,
    /// 自定义指标
    custom_metrics: Arc<RwLock<HashMap<String, MetricValue>>>,
    /// 时间序列数据
    time_series: Arc<RwLock<HashMap<String, Vec<TimeSeriesPoint>>>>,
}

/// 指标值类型
#[derive(Debug, Clone)]
pub enum MetricValue {
    Counter(u64),
    Gauge(f64),
    Histogram(Vec<f64>),
    Timer(Duration),
}

/// 时间序列数据点
#[derive(Debug, Clone)]
pub struct TimeSeriesPoint {
    pub timestamp: u64,
    pub value: f64,
}

impl MetricsCollector {
    /// 创建新的指标收集器
    pub fn new() -> Self {
        Self {
            arbitrage_stats: Arc::new(RwLock::new(ArbitrageStats {
                total_opportunities: 0,
                executed_trades: 0,
                total_profit: Decimal::ZERO,
                success_rate: 0.0,
                average_profit: Decimal::ZERO,
                last_updated: std::time::SystemTime::now()
                    .duration_since(std::time::UNIX_EPOCH)
                    .unwrap()
                    .as_secs(),
            })),
            performance_metrics: Arc::new(RwLock::new(PerformanceMetrics {
                total_opportunities: 0,
                executed_trades: 0,
                success_rate: 0.0,
                total_profit: Decimal::ZERO,
                average_profit: Decimal::ZERO,
                active_pools: 0,
                detection_latency_ms: 0,
                execution_latency_ms: 0,
                memory_usage_mb: 0,
                cpu_usage_percent: 0.0,
            })),
            custom_metrics: Arc::new(RwLock::new(HashMap::new())),
            time_series: Arc::new(RwLock::new(HashMap::new())),
        }
    }
    
    /// 启动指标收集
    pub async fn start_collection(&self, interval_secs: u64) {
        let performance_metrics = Arc::clone(&self.performance_metrics);
        let time_series = Arc::clone(&self.time_series);
        
        tokio::spawn(async move {
            let mut interval = interval(Duration::from_secs(interval_secs));
            
            loop {
                interval.tick().await;
                
                // 收集系统指标
                let memory_usage = Self::get_memory_usage();
                let cpu_usage = Self::get_cpu_usage();
                
                // 更新性能指标
                {
                    let mut metrics = performance_metrics.write().await;
                    metrics.memory_usage_mb = memory_usage;
                    metrics.cpu_usage_percent = cpu_usage;
                }
                
                // 记录时间序列数据
                let timestamp = std::time::SystemTime::now()
                    .duration_since(std::time::UNIX_EPOCH)
                    .unwrap()
                    .as_secs();
                
                Self::add_time_series_point(&time_series, "memory_usage", timestamp, memory_usage as f64).await;
                Self::add_time_series_point(&time_series, "cpu_usage", timestamp, cpu_usage).await;
            }
        });
    }
    
    /// 记录套利机会
    pub async fn record_opportunity(&self, opportunity: &ArbitrageOpportunity) {
        let mut stats = self.arbitrage_stats.write().await;
        stats.total_opportunities += 1;
        stats.last_updated = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_secs();
        
        // 更新性能指标
        let mut perf_metrics = self.performance_metrics.write().await;
        perf_metrics.total_opportunities += 1;
        
        // 记录时间序列
        let timestamp = opportunity.timestamp;
        self.add_time_series_point_internal("opportunities_per_hour", timestamp, 1.0).await;
        self.add_time_series_point_internal("profit_potential", timestamp, 
                                           opportunity.profit.to_f64().unwrap_or(0.0)).await;
    }
    
    /// 记录交易结果
    pub async fn record_trade_result(&self, result: &ArbitrageResult) {
        let mut stats = self.arbitrage_stats.write().await;
        
        if result.executed {
            stats.executed_trades += 1;
            
            if let Some(profit) = result.actual_profit {
                stats.total_profit += profit;
                
                // 计算平均利润
                if stats.executed_trades > 0 {
                    stats.average_profit = stats.total_profit / Decimal::from(stats.executed_trades);
                }
            }
        }
        
        // 计算成功率
        if stats.total_opportunities > 0 {
            stats.success_rate = stats.executed_trades as f64 / stats.total_opportunities as f64;
        }
        
        stats.last_updated = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_secs();
        
        // 更新性能指标
        let mut perf_metrics = self.performance_metrics.write().await;
        perf_metrics.executed_trades = stats.executed_trades;
        perf_metrics.success_rate = stats.success_rate;
        perf_metrics.total_profit = stats.total_profit;
        perf_metrics.average_profit = stats.average_profit;
        
        // 记录时间序列
        let timestamp = result.execution_time;
        if result.executed {
            self.add_time_series_point_internal("executed_trades", timestamp, 1.0).await;
            
            if let Some(profit) = result.actual_profit {
                self.add_time_series_point_internal("trade_profit", timestamp, 
                                                   profit.to_f64().unwrap_or(0.0)).await;
            }
        }
    }
    
    /// 记录检测延迟
    pub async fn record_detection_latency(&self, latency_ms: u64) {
        let mut perf_metrics = self.performance_metrics.write().await;
        perf_metrics.detection_latency_ms = latency_ms;
        
        let timestamp = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_secs();
        
        self.add_time_series_point_internal("detection_latency", timestamp, latency_ms as f64).await;
    }
    
    /// 记录执行延迟
    pub async fn record_execution_latency(&self, latency_ms: u64) {
        let mut perf_metrics = self.performance_metrics.write().await;
        perf_metrics.execution_latency_ms = latency_ms;
        
        let timestamp = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_secs();
        
        self.add_time_series_point_internal("execution_latency", timestamp, latency_ms as f64).await;
    }
    
    /// 更新活跃池子数量
    pub async fn update_active_pools(&self, count: usize) {
        let mut perf_metrics = self.performance_metrics.write().await;
        perf_metrics.active_pools = count;
        
        let timestamp = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_secs();
        
        self.add_time_series_point_internal("active_pools", timestamp, count as f64).await;
    }
    
    /// 增加自定义计数器
    pub async fn increment_counter(&self, name: &str, value: u64) {
        let mut metrics = self.custom_metrics.write().await;
        
        match metrics.get_mut(name) {
            Some(MetricValue::Counter(ref mut counter)) => {
                *counter += value;
            },
            _ => {
                metrics.insert(name.to_string(), MetricValue::Counter(value));
            }
        }
    }
    
    /// 设置仪表盘值
    pub async fn set_gauge(&self, name: &str, value: f64) {
        let mut metrics = self.custom_metrics.write().await;
        metrics.insert(name.to_string(), MetricValue::Gauge(value));
        
        let timestamp = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_secs();
        
        self.add_time_series_point_internal(name, timestamp, value).await;
    }
    
    /// 记录直方图数据
    pub async fn record_histogram(&self, name: &str, value: f64) {
        let mut metrics = self.custom_metrics.write().await;
        
        match metrics.get_mut(name) {
            Some(MetricValue::Histogram(ref mut values)) => {
                values.push(value);
                // 保持最近1000个值
                if values.len() > 1000 {
                    values.remove(0);
                }
            },
            _ => {
                metrics.insert(name.to_string(), MetricValue::Histogram(vec![value]));
            }
        }
    }
    
    /// 获取套利统计
    pub async fn get_arbitrage_stats(&self) -> ArbitrageStats {
        self.arbitrage_stats.read().await.clone()
    }
    
    /// 获取性能指标
    pub async fn get_performance_metrics(&self) -> PerformanceMetrics {
        self.performance_metrics.read().await.clone()
    }
    
    /// 获取自定义指标
    pub async fn get_custom_metrics(&self) -> HashMap<String, MetricValue> {
        self.custom_metrics.read().await.clone()
    }
    
    /// 获取时间序列数据
    pub async fn get_time_series(&self, metric_name: &str, duration_secs: u64) -> Vec<TimeSeriesPoint> {
        let time_series = self.time_series.read().await;
        
        if let Some(points) = time_series.get(metric_name) {
            let cutoff_time = std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap()
                .as_secs() - duration_secs;
            
            points.iter()
                .filter(|point| point.timestamp >= cutoff_time)
                .cloned()
                .collect()
        } else {
            Vec::new()
        }
    }
    
    /// 内部方法：添加时间序列数据点
    async fn add_time_series_point_internal(&self, metric_name: &str, timestamp: u64, value: f64) {
        Self::add_time_series_point(&self.time_series, metric_name, timestamp, value).await;
    }
    
    /// 静态方法：添加时间序列数据点
    async fn add_time_series_point(
        time_series: &Arc<RwLock<HashMap<String, Vec<TimeSeriesPoint>>>>,
        metric_name: &str,
        timestamp: u64,
        value: f64,
    ) {
        let mut ts = time_series.write().await;
        
        let points = ts.entry(metric_name.to_string()).or_insert_with(Vec::new);
        points.push(TimeSeriesPoint { timestamp, value });
        
        // 保持最近24小时的数据
        let cutoff_time = timestamp.saturating_sub(24 * 60 * 60);
        points.retain(|point| point.timestamp >= cutoff_time);
    }
    
    /// 获取内存使用量（MB）
    fn get_memory_usage() -> u64 {
        // 简化实现，实际应该使用系统API
        // 这里返回一个模拟值
        100 + (std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_secs() % 100)
    }
    
    /// 获取CPU使用率（百分比）
    fn get_cpu_usage() -> f64 {
        // 简化实现，实际应该使用系统API
        // 这里返回一个模拟值
        10.0 + (std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_secs() % 50) as f64
    }
    
    /// 重置所有指标
    pub async fn reset_metrics(&self) {
        let mut stats = self.arbitrage_stats.write().await;
        *stats = ArbitrageStats {
            total_opportunities: 0,
            executed_trades: 0,
            total_profit: Decimal::ZERO,
            success_rate: 0.0,
            average_profit: Decimal::ZERO,
            last_updated: std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap()
                .as_secs(),
        };
        
        let mut custom_metrics = self.custom_metrics.write().await;
        custom_metrics.clear();
        
        let mut time_series = self.time_series.write().await;
        time_series.clear();
    }
}

impl Default for MetricsCollector {
    fn default() -> Self {
        Self::new()
    }
}
