use crate::types::{PoolS<PERSON>, Token, TradingGraph, ArbitrageOpportunity, Result, ArbitrageError};
use crate::algorithms::{BellmanFord, TernarySearch};
use crate::data::price_calculator::PriceCalculator;
use std::collections::HashMap;

/// 简化的套利检测器
pub struct SimpleArbitrageDetector {
    pools: HashMap<String, PoolState>,
    bellman_ford: BellmanFord,
}

impl SimpleArbitrageDetector {
    /// 创建新的套利检测器
    pub fn new() -> Self {
        Self {
            pools: HashMap::new(),
            bellman_ford: BellmanFord::new(),
        }
    }
    
    /// 添加池子
    pub fn add_pool(&mut self, pool: PoolState) {
        self.pools.insert(pool.pool_id.clone(), pool);
    }
    
    /// 构建交易图
    pub fn build_trading_graph(&self) -> TradingGraph {
        let mut graph = TradingGraph::new();
        
        // 添加所有代币节点
        for pool in self.pools.values() {
            graph.add_node(pool.metadata.token0.clone());
            graph.add_node(pool.metadata.token1.clone());
        }
        
        // 添加交易边（双向）
        for pool in self.pools.values() {
            let price = pool.price;
            if price > 0.0 {
                // token0 -> token1
                let weight_0_to_1 = -price.ln(); // 负对数价格
                graph.add_edge(
                    pool.metadata.token0.clone(),
                    pool.metadata.token1.clone(),
                    weight_0_to_1,
                    pool.pool_id.clone(),
                );
                
                // token1 -> token0
                let weight_1_to_0 = -(1.0 / price).ln();
                graph.add_edge(
                    pool.metadata.token1.clone(),
                    pool.metadata.token0.clone(),
                    weight_1_to_0,
                    pool.pool_id.clone(),
                );
            }
        }
        
        graph
    }
    
    /// 检测套利机会
    pub fn detect_arbitrage_opportunities(&mut self) -> Result<Vec<ArbitrageOpportunity>> {
        let graph = self.build_trading_graph();
        let mut opportunities = Vec::new();
        
        // 对每个代币作为起点检测负环
        for node in &graph.nodes {
            let has_negative_cycle = self.bellman_ford.detect_arbitrage(&graph, &node.token)?;
            
            if has_negative_cycle {
                // 找到负环路径
                if let Ok(cycle_path) = self.bellman_ford.get_negative_cycle_path(&graph) {
                    // 计算套利机会
                    if let Ok(opportunity) = self.calculate_arbitrage_opportunity(&cycle_path, &graph) {
                        opportunities.push(opportunity);
                    }
                }
            }
        }
        
        // 去重和排序
        opportunities.sort_by(|a, b| b.profit.partial_cmp(&a.profit).unwrap_or(std::cmp::Ordering::Equal));
        opportunities.dedup_by(|a, b| a.id == b.id);
        
        Ok(opportunities)
    }
    
    /// 计算套利机会详情
    fn calculate_arbitrage_opportunity(
        &self,
        path: &[Token],
        graph: &TradingGraph,
    ) -> Result<ArbitrageOpportunity> {
        if path.len() < 3 {
            return Err(ArbitrageError::ArbitrageDetectionError("路径太短".to_string()));
        }
        
        // 计算路径权重（负对数价格）
        let path_weight = self.bellman_ford.calculate_path_weight(path, graph)?;
        
        // 如果权重为负，说明存在套利机会
        if path_weight >= 0.0 {
            return Err(ArbitrageError::ArbitrageDetectionError("没有套利机会".to_string()));
        }
        
        // 计算理论利润率
        let _profit_ratio = (-path_weight).exp() - 1.0;
        
        // 使用三分搜索找到最优交易量
        let max_amount = 1000.0; // 最大交易量
        let optimal_amount = self.find_optimal_trade_amount(path, max_amount)?;
        
        // 计算预期输出和利润
        let expected_output = self.simulate_trade_path(path, optimal_amount)?;
        let profit = expected_output - optimal_amount;
        let profit_percentage = if optimal_amount > 0.0 { profit / optimal_amount } else { 0.0 };
        
        // 提取池子ID
        let pools = self.extract_pool_ids(path, graph)?;
        
        Ok(ArbitrageOpportunity {
            id: format!("arb_{}", uuid::Uuid::new_v4()),
            tokens: path.to_vec(),
            pools,
            input_amount: optimal_amount,
            expected_output,
            profit,
            profit_percentage,
            timestamp: std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap_or_default()
                .as_secs(),
        })
    }
    
    /// 找到最优交易量
    fn find_optimal_trade_amount(&self, path: &[Token], max_amount: f64) -> Result<f64> {
        let profit_function = |amount: f64| -> Result<f64> {
            if amount <= 0.0 {
                return Ok(0.0);
            }
            
            let output = self.simulate_trade_path(path, amount)?;
            let profit = output - amount;
            Ok(profit)
        };
        
        TernarySearch::find_optimal_amount(
            profit_function,
            0.1,      // 最小交易量
            max_amount,
            0.01,     // 精度
            100,      // 最大迭代次数
        )
    }
    
    /// 模拟交易路径
    fn simulate_trade_path(&self, path: &[Token], initial_amount: f64) -> Result<f64> {
        let mut current_amount = initial_amount;
        
        for i in 0..(path.len() - 1) {
            let from_token = &path[i];
            let to_token = &path[i + 1];
            
            // 找到对应的池子
            let pool = self.find_pool_for_pair(from_token, to_token)?;
            
            // 计算输出金额
            let zero_for_one = from_token.address == pool.metadata.token0.address;
            current_amount = PriceCalculator::calculate_amount_out_with_direction(
                current_amount,
                pool,
                zero_for_one,
            )?;
        }
        
        Ok(current_amount)
    }
    
    /// 找到交易对对应的池子
    fn find_pool_for_pair(&self, token_a: &Token, token_b: &Token) -> Result<&PoolState> {
        for pool in self.pools.values() {
            let has_token_a = pool.metadata.token0.address == token_a.address 
                || pool.metadata.token1.address == token_a.address;
            let has_token_b = pool.metadata.token0.address == token_b.address 
                || pool.metadata.token1.address == token_b.address;
            
            if has_token_a && has_token_b {
                return Ok(pool);
            }
        }
        
        Err(ArbitrageError::ArbitrageDetectionError(
            format!("找不到 {}-{} 交易对的池子", token_a.symbol, token_b.symbol)
        ))
    }
    
    /// 提取路径中的池子ID
    fn extract_pool_ids(&self, path: &[Token], graph: &TradingGraph) -> Result<Vec<String>> {
        let mut pool_ids = Vec::new();
        
        for i in 0..(path.len() - 1) {
            let from = &path[i];
            let to = &path[i + 1];
            
            // 在图中找到对应的边
            let edge = graph.edges.iter()
                .find(|edge| &edge.from == from && &edge.to == to)
                .ok_or_else(|| ArbitrageError::ArbitrageDetectionError(
                    format!("找不到从 {} 到 {} 的边", from.symbol, to.symbol)
                ))?;
            
            pool_ids.push(edge.pool_id.clone());
        }
        
        Ok(pool_ids)
    }
}

impl Default for SimpleArbitrageDetector {
    fn default() -> Self {
        Self::new()
    }
}
