use std::collections::HashMap;
use crate::types::{Token, TradingGraph, GraphEdge, Result, ArbitrageError};

/// Bellman-Ford算法实现，用于检测负环（套利机会）
pub struct BellmanFord {
    /// 距离数组 (token -> 最短距离)
    distances: HashMap<Token, f64>,
    /// 前驱数组 (token -> 前驱token)
    predecessors: HashMap<Token, Option<Token>>,
    /// 是否存在负环
    has_negative_cycle: bool,
}

impl BellmanFord {
    /// 创建新的Bellman-Ford实例
    pub fn new() -> Self {
        Self {
            distances: HashMap::new(),
            predecessors: HashMap::new(),
            has_negative_cycle: false,
        }
    }
    
    /// 运行优化版Bellman-Ford算法检测负环（SPFA优化）
    pub fn detect_arbitrage(&mut self, graph: &TradingGraph, start_token: &Token) -> Result<bool> {
        if graph.nodes.is_empty() {
            return Err(ArbitrageError::ArbitrageDetectionError("图为空".to_string()));
        }

        // 初始化距离
        self.initialize_distances(graph, start_token);

        // 使用SPFA算法优化
        let mut queue = std::collections::VecDeque::new();
        let mut in_queue = std::collections::HashMap::new();
        let mut update_count = std::collections::HashMap::new();

        // 将起始节点加入队列
        queue.push_back(start_token.clone());
        in_queue.insert(start_token.clone(), true);
        update_count.insert(start_token.clone(), 0);

        let node_count = graph.nodes.len();

        while let Some(current_token) = queue.pop_front() {
            in_queue.insert(current_token.clone(), false);

            // 检查是否超过更新次数限制（检测负环）
            let count = update_count.get(&current_token).copied().unwrap_or(0);
            if count >= node_count {
                self.has_negative_cycle = true;
                return Ok(true);
            }

            // 松弛从当前节点出发的所有边
            for edge in graph.get_edges_from(&current_token) {
                if self.relax_edge(edge) {
                    // 如果目标节点不在队列中，加入队列
                    if !in_queue.get(&edge.to).copied().unwrap_or(false) {
                        queue.push_back(edge.to.clone());
                        in_queue.insert(edge.to.clone(), true);
                        let new_count = update_count.get(&edge.to).copied().unwrap_or(0) + 1;
                        update_count.insert(edge.to.clone(), new_count);
                    }
                }
            }
        }

        self.has_negative_cycle = false;
        Ok(false)
    }
    
    /// 初始化距离数组
    fn initialize_distances(&mut self, graph: &TradingGraph, start_token: &Token) {
        self.distances.clear();
        self.predecessors.clear();
        
        // 将所有节点的距离初始化为正无穷
        for node in &graph.nodes {
            self.distances.insert(node.token.clone(), Decimal::MAX);
            self.predecessors.insert(node.token.clone(), None);
        }
        
        // 起始节点距离为0
        self.distances.insert(start_token.clone(), Decimal::ZERO);
    }
    
    /// 松弛边
    fn relax_edge(&mut self, edge: &GraphEdge) -> bool {
        let from_distance = self.distances.get(&edge.from).copied()
            .unwrap_or(Decimal::MAX);
        
        if from_distance == Decimal::MAX {
            return false;
        }
        
        let new_distance = from_distance + edge.weight;
        let current_distance = self.distances.get(&edge.to).copied()
            .unwrap_or(Decimal::MAX);
        
        if new_distance < current_distance {
            self.distances.insert(edge.to.clone(), new_distance);
            self.predecessors.insert(edge.to.clone(), Some(edge.from.clone()));
            return true;
        }
        
        false
    }
    
    /// 检查是否存在负环
    fn check_negative_cycle(&mut self, graph: &TradingGraph) -> bool {
        for edge in &graph.edges {
            let from_distance = self.distances.get(&edge.from).copied()
                .unwrap_or(Decimal::MAX);
            
            if from_distance == Decimal::MAX {
                continue;
            }
            
            let new_distance = from_distance + edge.weight;
            let current_distance = self.distances.get(&edge.to).copied()
                .unwrap_or(Decimal::MAX);
            
            if new_distance < current_distance {
                return true; // 发现负环
            }
        }
        
        false
    }
    
    /// 获取负环路径
    pub fn get_negative_cycle_path(&self, graph: &TradingGraph) -> Result<Vec<Token>> {
        if !self.has_negative_cycle {
            return Err(ArbitrageError::ArbitrageDetectionError("没有发现负环".to_string()));
        }
        
        // 找到负环中的一个节点
        let mut cycle_node = None;
        for edge in &graph.edges {
            let from_distance = self.distances.get(&edge.from).copied()
                .unwrap_or(Decimal::MAX);
            
            if from_distance == Decimal::MAX {
                continue;
            }
            
            let new_distance = from_distance + edge.weight;
            let current_distance = self.distances.get(&edge.to).copied()
                .unwrap_or(Decimal::MAX);
            
            if new_distance < current_distance {
                cycle_node = Some(edge.to.clone());
                break;
            }
        }
        
        let cycle_start = cycle_node.ok_or_else(|| {
            ArbitrageError::ArbitrageDetectionError("无法找到负环节点".to_string())
        })?;
        
        // 构建负环路径
        let mut path = Vec::new();
        let mut current = cycle_start.clone();
        let mut visited = std::collections::HashSet::new();
        
        loop {
            if visited.contains(&current) {
                // 找到环的起点，截取环部分
                if let Some(cycle_start_index) = path.iter().position(|token| token == &current) {
                    path = path[cycle_start_index..].to_vec();
                    path.push(current); // 闭合环
                }
                break;
            }
            
            visited.insert(current.clone());
            path.push(current.clone());
            
            if let Some(Some(predecessor)) = self.predecessors.get(&current) {
                current = predecessor.clone();
            } else {
                break;
            }
        }
        
        Ok(path)
    }
    
    /// 计算路径的总权重（负对数价格）
    pub fn calculate_path_weight(&self, path: &[Token], graph: &TradingGraph) -> Result<Decimal> {
        if path.len() < 2 {
            return Ok(Decimal::ZERO);
        }
        
        let mut total_weight = Decimal::ZERO;
        
        for i in 0..(path.len() - 1) {
            let from = &path[i];
            let to = &path[i + 1];
            
            // 找到对应的边
            let edge = graph.edges.iter()
                .find(|edge| &edge.from == from && &edge.to == to)
                .ok_or_else(|| ArbitrageError::ArbitrageDetectionError(
                    format!("找不到从 {} 到 {} 的边", from.symbol, to.symbol)
                ))?;
            
            total_weight += edge.weight;
        }
        
        Ok(total_weight)
    }
    
    /// 获取最短距离
    pub fn get_distance(&self, token: &Token) -> Option<Decimal> {
        self.distances.get(token).copied()
    }
    
    /// 获取到指定代币的最短路径
    pub fn get_shortest_path(&self, target: &Token) -> Result<Vec<Token>> {
        let mut path = Vec::new();
        let mut current = target.clone();
        
        while let Some(Some(predecessor)) = self.predecessors.get(&current) {
            path.push(current.clone());
            current = predecessor.clone();
        }
        
        path.push(current); // 添加起始节点
        path.reverse();
        
        if path.is_empty() {
            return Err(ArbitrageError::ArbitrageDetectionError(
                format!("无法找到到 {} 的路径", target.symbol)
            ));
        }
        
        Ok(path)
    }
    
    /// 检查是否存在负环
    pub fn has_negative_cycle(&self) -> bool {
        self.has_negative_cycle
    }
}

impl Default for BellmanFord {
    fn default() -> Self {
        Self::new()
    }
}
