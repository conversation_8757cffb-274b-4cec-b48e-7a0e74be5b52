use crate::types::{Result, ArbitrageError};

/// 三分搜索算法，用于寻找最优交易量
pub struct TernarySearch;

impl TernarySearch {
    /// 使用三分搜索找到最大化利润的交易量
    ///
    /// # 参数
    /// - `profit_function`: 利润函数，输入交易量，返回利润
    /// - `left`: 搜索区间左端点
    /// - `right`: 搜索区间右端点
    /// - `epsilon`: 精度要求
    /// - `max_iterations`: 最大迭代次数
    pub fn find_optimal_amount<F>(
        profit_function: F,
        left: f64,
        right: f64,
        epsilon: f64,
        max_iterations: usize,
    ) -> Result<f64>
    where
        F: Fn(f64) -> Result<f64>,
    {
        if left >= right {
            return Err(ArbitrageError::InternalError("搜索区间无效".to_string()));
        }

        if epsilon <= 0.0 {
            return Err(ArbitrageError::InternalError("精度必须大于0".to_string()));
        }

        let mut l = left;
        let mut r = right;
        let mut iterations = 0;

        while r - l > epsilon && iterations < max_iterations {
            let delta = (r - l) / 3.0;
            let m1 = l + delta;
            let m2 = r - delta;

            let profit1 = profit_function(m1)?;
            let profit2 = profit_function(m2)?;

            if profit1 < profit2 {
                l = m1;
            } else {
                r = m2;
            }

            iterations += 1;
        }

        Ok((l + r) / 2.0)
    }
    
    /// 寻找最优交易量（考虑多个约束条件）
    pub fn find_optimal_amount_with_constraints<F, C>(
        profit_function: F,
        constraint_function: C,
        left: f64,
        right: f64,
        epsilon: f64,
        max_iterations: usize,
    ) -> Result<f64>
    where
        F: Fn(f64) -> Result<f64>,
        C: Fn(f64) -> Result<bool>,
    {
        // 首先检查边界是否满足约束
        if !constraint_function(left)? {
            return Err(ArbitrageError::InternalError("左边界不满足约束".to_string()));
        }
        
        if !constraint_function(right)? {
            return Err(ArbitrageError::InternalError("右边界不满足约束".to_string()));
        }
        
        let mut l = left;
        let mut r = right;
        let mut iterations = 0;
        
        while r - l > epsilon && iterations < max_iterations {
            let delta = (r - l) / 3.0;
            let m1 = l + delta;
            let m2 = r - delta;
            
            // 检查约束条件
            let m1_valid = constraint_function(m1)?;
            let m2_valid = constraint_function(m2)?;
            
            if !m1_valid && !m2_valid {
                return Err(ArbitrageError::InternalError("搜索区间内没有满足约束的点".to_string()));
            }
            
            if !m1_valid {
                l = m1;
                iterations += 1;
                continue;
            }
            
            if !m2_valid {
                r = m2;
                iterations += 1;
                continue;
            }
            
            let profit1 = profit_function(m1)?;
            let profit2 = profit_function(m2)?;
            
            if profit1 < profit2 {
                l = m1;
            } else {
                r = m2;
            }
            
            iterations += 1;
        }
        
        let optimal = (l + r) / 2.0;
        
        // 最终检查结果是否满足约束
        if !constraint_function(optimal)? {
            return Err(ArbitrageError::InternalError("最优解不满足约束".to_string()));
        }
        
        Ok(optimal)
    }
    
    /// 寻找函数的最大值点
    pub fn find_maximum<F>(
        function: F,
        left: f64,
        right: f64,
        epsilon: f64,
    ) -> Result<(f64, f64)>
    where
        F: Fn(f64) -> Result<f64>,
    {
        let optimal_x = Self::find_optimal_amount(
            &function,
            left,
            right,
            epsilon,
            1000, // 默认最大迭代次数
        )?;
        
        let optimal_y = function(optimal_x)?;
        
        Ok((optimal_x, optimal_y))
    }
    
    /// 寻找函数的最小值点
    pub fn find_minimum<F>(
        function: F,
        left: Decimal,
        right: Decimal,
        epsilon: Decimal,
    ) -> Result<(Decimal, Decimal)>
    where
        F: Fn(Decimal) -> Result<Decimal>,
    {
        // 通过取负值来转换为最大值问题
        let negative_function = |x: Decimal| -> Result<Decimal> {
            Ok(-function(x)?)
        };
        
        let (optimal_x, negative_optimal_y) = Self::find_maximum(
            negative_function,
            left,
            right,
            epsilon,
        )?;
        
        Ok((optimal_x, -negative_optimal_y))
    }
    
    /// 验证函数在给定区间内是否单峰
    pub fn verify_unimodal<F>(
        function: F,
        left: Decimal,
        right: Decimal,
        sample_points: usize,
    ) -> Result<bool>
    where
        F: Fn(Decimal) -> Result<Decimal>,
    {
        if sample_points < 3 {
            return Err(ArbitrageError::InternalError("采样点数必须至少为3".to_string()));
        }
        
        let step = (right - left) / Decimal::from(sample_points - 1);
        let mut values = Vec::new();
        
        for i in 0..sample_points {
            let x = left + step * Decimal::from(i);
            let y = function(x)?;
            values.push(y);
        }
        
        // 检查是否存在唯一的峰值
        let mut peak_count = 0;
        
        for i in 1..(values.len() - 1) {
            if values[i] > values[i - 1] && values[i] > values[i + 1] {
                peak_count += 1;
            }
        }
        
        Ok(peak_count <= 1)
    }
    
    /// 自适应三分搜索（根据函数特性调整精度）
    pub fn adaptive_search<F>(
        profit_function: F,
        left: Decimal,
        right: Decimal,
        initial_epsilon: Decimal,
        min_epsilon: Decimal,
        max_iterations: usize,
    ) -> Result<Decimal>
    where
        F: Fn(Decimal) -> Result<Decimal>,
    {
        let mut epsilon = initial_epsilon;
        let mut best_result = None;
        let mut iterations_used = 0;
        
        while epsilon >= min_epsilon && iterations_used < max_iterations {
            let remaining_iterations = max_iterations - iterations_used;
            let current_max_iterations = (remaining_iterations / 2).max(10);
            
            match Self::find_optimal_amount(
                &profit_function,
                left,
                right,
                epsilon,
                current_max_iterations,
            ) {
                Ok(result) => {
                    best_result = Some(result);
                    break;
                },
                Err(_) => {
                    epsilon = epsilon / Decimal::from(2); // 降低精度要求
                    iterations_used += current_max_iterations;
                }
            }
        }
        
        best_result.ok_or_else(|| {
            ArbitrageError::InternalError("自适应搜索失败".to_string())
        })
    }
}
