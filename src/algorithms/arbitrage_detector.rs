use std::collections::HashSet;
use rust_decimal::Decimal;
use num_traits::{FromPrimitive, ToPrimitive};
use uuid::Uuid;

use crate::types::{
    Token, PoolState, TradingGraph, GraphEdge,
    ArbitrageOpportunity, TradePath, ArbitrageConfig,
    Result, ArbitrageError
};
use crate::algorithms::{BellmanFord, TernarySearch};
use crate::data::{PoolManager, PriceCalculator};

/// 套利机会检测器
pub struct ArbitrageDetector {
    config: ArbitrageConfig,
    pub pool_manager: PoolManager,
}

impl ArbitrageDetector {
    /// 创建新的套利检测器
    pub fn new(config: ArbitrageConfig, pool_manager: PoolManager) -> Self {
        Self {
            config,
            pool_manager,
        }
    }
    
    /// 检测所有套利机会
    pub async fn detect_opportunities(&self) -> Result<Vec<ArbitrageOpportunity>> {
        log::info!("开始检测套利机会...");
        
        // 构建交易图
        let graph = self.build_trading_graph().await?;
        
        if graph.nodes.is_empty() {
            return Ok(Vec::new());
        }
        
        let mut opportunities = Vec::new();
        
        // 对每个代币作为起点运行Bellman-Ford算法
        for node in &graph.nodes {
            let token_opportunities = self.detect_opportunities_from_token(&graph, &node.token).await?;
            opportunities.extend(token_opportunities);
        }
        
        // 过滤和排序机会
        let filtered_opportunities = self.filter_and_rank_opportunities(opportunities).await?;
        
        log::info!("检测到 {} 个套利机会", filtered_opportunities.len());
        Ok(filtered_opportunities)
    }
    
    /// 从指定代币开始检测套利机会
    async fn detect_opportunities_from_token(
        &self,
        graph: &TradingGraph,
        start_token: &Token,
    ) -> Result<Vec<ArbitrageOpportunity>> {
        let mut bellman_ford = BellmanFord::new();
        
        // 运行Bellman-Ford算法
        let has_negative_cycle = bellman_ford.detect_arbitrage(graph, start_token)?;
        
        if !has_negative_cycle {
            return Ok(Vec::new());
        }
        
        // 获取负环路径
        let cycle_path = bellman_ford.get_negative_cycle_path(graph)?;
        
        if cycle_path.len() < 3 {
            return Ok(Vec::new()); // 至少需要3个节点形成有效的套利路径
        }
        
        // 计算最优交易量和利润
        let opportunity = self.calculate_arbitrage_opportunity(&cycle_path, graph).await?;
        
        Ok(vec![opportunity])
    }
    
    /// 构建交易图
    async fn build_trading_graph(&self) -> Result<TradingGraph> {
        let mut graph = TradingGraph::new();
        let pools = self.pool_manager.get_all_pools();
        
        // 添加所有代币节点
        let mut tokens = HashSet::new();
        for pool in &pools {
            tokens.insert(pool.metadata.token0.clone());
            tokens.insert(pool.metadata.token1.clone());
        }
        
        for token in tokens {
            graph.add_node(token);
        }
        
        // 添加边（每个池子对应两条边：双向交易）
        for pool in &pools {
            if !self.pool_manager.is_pool_active(&pool.canister_id) {
                continue;
            }
            
            // token0 -> token1
            let edge1 = self.create_graph_edge(
                &pool.metadata.token0,
                &pool.metadata.token1,
                &pool,
                false, // 不是反向
            )?;
            graph.add_edge(edge1);
            
            // token1 -> token0
            let edge2 = self.create_graph_edge(
                &pool.metadata.token1,
                &pool.metadata.token0,
                &pool,
                true, // 反向
            )?;
            graph.add_edge(edge2);
        }
        
        Ok(graph)
    }
    
    /// 创建图边
    fn create_graph_edge(
        &self,
        from: &Token,
        to: &Token,
        pool: &PoolState,
        is_reverse: bool,
    ) -> Result<GraphEdge> {
        // 计算权重（负对数价格）
        let price = if is_reverse {
            pool.inverse_price
        } else {
            pool.price
        };
        
        if price <= Decimal::ZERO {
            return Err(ArbitrageError::PriceCalculationError(
                format!("池子 {} 价格无效: {}", pool.canister_id, price)
            ));
        }
        
        // 考虑手续费的影响
        let fee_rate = Decimal::from(pool.metadata.fee) / Decimal::from(1000000);
        let effective_price = price * (Decimal::ONE - fee_rate);
        
        // 权重 = -ln(effective_price)
        // 使用f64进行对数计算，然后转换回Decimal
        let price_f64 = effective_price.to_f64().unwrap_or(1.0);
        let weight_f64 = -price_f64.ln();
        let weight = Decimal::from_f64(weight_f64).unwrap_or(Decimal::ZERO);
        
        Ok(GraphEdge {
            from: from.clone(),
            to: to.clone(),
            pool_id: pool.canister_id.clone(),
            weight,
            fee: pool.metadata.fee,
            liquidity: pool.metadata.liquidity,
        })
    }
    
    /// 计算套利机会
    async fn calculate_arbitrage_opportunity(
        &self,
        path: &[Token],
        graph: &TradingGraph,
    ) -> Result<ArbitrageOpportunity> {
        if path.len() < 3 {
            return Err(ArbitrageError::ArbitrageDetectionError(
                "套利路径太短".to_string()
            ));
        }
        
        // 构建交易路径
        let trade_path = self.build_trade_path(path, graph)?;
        
        // 使用三分搜索找到最优交易量
        let optimal_amount = self.find_optimal_trade_amount(&trade_path).await?;
        
        // 计算预期输出和利润
        let expected_output = self.calculate_path_output(optimal_amount, &trade_path).await?;
        let profit = expected_output - optimal_amount;
        let profit_percentage = if optimal_amount > Decimal::ZERO {
            profit / optimal_amount
        } else {
            Decimal::ZERO
        };
        
        // 估算Gas成本
        let gas_cost = self.estimate_gas_cost(&trade_path)?;
        let net_profit = profit - gas_cost;
        
        // 计算置信度
        let confidence = self.calculate_confidence(&trade_path, optimal_amount)?;
        
        Ok(ArbitrageOpportunity {
            id: Uuid::new_v4().to_string(),
            path: trade_path,
            input_amount: optimal_amount,
            expected_output,
            profit,
            profit_percentage,
            gas_cost,
            net_profit,
            confidence,
            timestamp: std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap()
                .as_secs(),
        })
    }
    
    /// 构建交易路径
    fn build_trade_path(&self, tokens: &[Token], graph: &TradingGraph) -> Result<TradePath> {
        let mut pools = Vec::new();
        let mut total_fee = 0u64;
        
        for i in 0..(tokens.len() - 1) {
            let from = &tokens[i];
            let to = &tokens[i + 1];
            
            // 找到对应的边
            let edge = graph.edges.iter()
                .find(|edge| &edge.from == from && &edge.to == to)
                .ok_or_else(|| ArbitrageError::ArbitrageDetectionError(
                    format!("找不到从 {} 到 {} 的池子", from.symbol, to.symbol)
                ))?;
            
            pools.push(edge.pool_id.clone());
            total_fee += edge.fee;
        }
        
        Ok(TradePath {
            pools,
            tokens: tokens.to_vec(),
            total_fee,
            expected_output: Decimal::ZERO, // 稍后计算
        })
    }
    
    /// 寻找最优交易量（考虑多重约束）
    async fn find_optimal_trade_amount(&self, path: &TradePath) -> Result<Decimal> {
        let profit_function = |amount: Decimal| -> Result<Decimal> {
            if amount <= Decimal::ZERO {
                return Ok(Decimal::ZERO);
            }

            // 计算路径净利润（考虑所有成本）
            let output = self.calculate_path_output_sync(amount, path)?;
            let gas_cost = self.estimate_gas_cost_for_amount(amount, path)?;
            let net_profit = output - amount - gas_cost;

            Ok(net_profit)
        };

        let constraint_function = |amount: Decimal| -> Result<bool> {
            // 多重约束检查
            if amount <= Decimal::ZERO || amount > self.config.max_trade_amount {
                return Ok(false);
            }

            // 检查每个池子的流动性约束
            for pool_id in &path.pools {
                if let Some(pool) = self.pool_manager.get_pool(pool_id) {
                    let max_trade_for_pool = Decimal::from(pool.metadata.liquidity) / Decimal::from(10); // 最多使用10%的流动性
                    if amount > max_trade_for_pool {
                        return Ok(false);
                    }
                } else {
                    return Ok(false);
                }
            }

            Ok(true)
        };

        TernarySearch::find_optimal_amount_with_constraints(
            profit_function,
            constraint_function,
            Decimal::from_f64(0.1).unwrap_or(Decimal::ONE), // 最小交易量
            self.config.max_trade_amount,
            Decimal::from_f64(0.001).unwrap_or(Decimal::ONE), // 提高精度
            200, // 增加最大迭代次数
        )
    }
    
    /// 计算路径输出（异步版本）
    async fn calculate_path_output(&self, amount: Decimal, path: &TradePath) -> Result<Decimal> {
        self.calculate_path_output_sync(amount, path)
    }
    
    /// 计算路径输出（同步版本）
    fn calculate_path_output_sync(&self, amount: Decimal, path: &TradePath) -> Result<Decimal> {
        let mut current_amount = amount;
        
        for (i, pool_id) in path.pools.iter().enumerate() {
            let pool = self.pool_manager.get_pool(pool_id)
                .ok_or_else(|| ArbitrageError::ArbitrageDetectionError(
                    format!("找不到池子: {}", pool_id)
                ))?;
            
            // 确定交易方向
            let token_in = if i == 0 {
                &path.tokens[0]
            } else {
                &path.tokens[i]
            };
            let token_out = &path.tokens[i + 1];

            current_amount = PriceCalculator::calculate_amount_out_by_tokens(
                current_amount,
                &pool,
                token_in,
                token_out
            )?;
        }
        
        Ok(current_amount)
    }
    
    /// 估算Gas成本（基于路径复杂度和交易量）
    fn estimate_gas_cost(&self, path: &TradePath) -> Result<Decimal> {
        let base_cost = Decimal::from_f64(0.1).unwrap_or(Decimal::ZERO); // 基础成本
        let per_hop_cost = Decimal::from_f64(0.05).unwrap_or(Decimal::ZERO); // 每跳成本
        let complexity_multiplier = Decimal::from_f64(1.0 + (path.pools.len() as f64 - 1.0) * 0.2)
            .unwrap_or(Decimal::ONE);

        let total_cost = (base_cost + per_hop_cost * Decimal::from(path.pools.len())) * complexity_multiplier;

        Ok(total_cost)
    }

    /// 基于交易量估算Gas成本
    fn estimate_gas_cost_for_amount(&self, amount: Decimal, path: &TradePath) -> Result<Decimal> {
        let base_gas_cost = self.estimate_gas_cost(path)?;

        // 大额交易可能需要更多Gas
        let amount_multiplier = if amount > Decimal::from(10000) {
            Decimal::from_f64(1.5).unwrap_or(Decimal::ONE)
        } else if amount > Decimal::from(1000) {
            Decimal::from_f64(1.2).unwrap_or(Decimal::ONE)
        } else {
            Decimal::ONE
        };

        Ok(base_gas_cost * amount_multiplier)
    }
    
    /// 计算置信度（基于多个因素）
    fn calculate_confidence(&self, path: &TradePath, amount: Decimal) -> Result<f64> {
        let mut confidence: f64 = 1.0;

        // 1. 路径长度影响（路径越长，风险越高）
        let path_length_penalty = match path.pools.len() {
            2 => 0.0,           // 直接交易，最高置信度
            3 => 0.1,           // 一跳交易
            4 => 0.25,          // 两跳交易
            _ => 0.4,           // 更多跳数，显著降低置信度
        };
        confidence -= path_length_penalty;

        // 2. 交易量影响（相对于最大交易量）
        let amount_ratio = amount.to_f64().unwrap_or(0.0) /
            self.config.max_trade_amount.to_f64().unwrap_or(1.0);
        let amount_penalty = if amount_ratio > 0.8 {
            0.3 // 超大额交易
        } else if amount_ratio > 0.5 {
            0.15 // 大额交易
        } else if amount_ratio > 0.2 {
            0.05 // 中等交易
        } else {
            0.0 // 小额交易
        };
        confidence -= amount_penalty;

        // 3. 流动性深度影响
        let mut min_liquidity_ratio: f64 = 1.0;
        for pool_id in &path.pools {
            if let Some(pool) = self.pool_manager.get_pool(pool_id) {
                let pool_liquidity = pool.metadata.liquidity as f64;
                let trade_ratio = amount.to_f64().unwrap_or(0.0) / pool_liquidity;
                min_liquidity_ratio = min_liquidity_ratio.min(1.0 - trade_ratio);
            }
        }

        if min_liquidity_ratio < 0.5 {
            confidence -= 0.3; // 流动性不足
        } else if min_liquidity_ratio < 0.8 {
            confidence -= 0.1; // 流动性偏低
        }

        // 4. 池子活跃度影响
        let mut active_pools = 0;
        for pool_id in &path.pools {
            if self.pool_manager.is_pool_active(pool_id) {
                active_pools += 1;
            }
        }

        if active_pools < path.pools.len() {
            confidence -= 0.2; // 有非活跃池子
        }

        // 5. 手续费影响
        let total_fee_rate = path.total_fee as f64 / 1000000.0; // 转换为小数
        if total_fee_rate > 0.01 { // 超过1%
            confidence -= 0.1;
        }

        Ok(confidence.max(0.0).min(1.0))
    }
    
    /// 过滤和排序套利机会
    async fn filter_and_rank_opportunities(
        &self,
        mut opportunities: Vec<ArbitrageOpportunity>,
    ) -> Result<Vec<ArbitrageOpportunity>> {
        // 过滤不符合条件的机会
        opportunities.retain(|opp| {
            opp.net_profit >= self.config.min_profit_threshold &&
            opp.profit_percentage >= self.config.min_profit_percentage &&
            opp.confidence >= self.config.confidence_threshold &&
            opp.path.pools.len() <= self.config.max_path_length
        });
        
        // 按风险调整后的利润排序
        opportunities.sort_by(|a, b| {
            b.risk_adjusted_profit().partial_cmp(&a.risk_adjusted_profit())
                .unwrap_or(std::cmp::Ordering::Equal)
        });
        
        Ok(opportunities)
    }
}
