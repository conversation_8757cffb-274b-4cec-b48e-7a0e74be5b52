use serde::{Deserialize, Serialize};
use rust_decimal::Decimal;
use num_traits::FromPrimitive;
use crate::types::{ArbitrageConfig, Result, ArbitrageError};

/// 系统配置
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct Settings {
    pub network: NetworkConfig,
    pub arbitrage: ArbitrageConfig,
    pub data: DataConfig,
    pub execution: ExecutionConfig,
    pub monitoring: MonitoringConfig,
    pub risk: RiskConfig,
}

/// 网络配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NetworkConfig {
    pub ic_gateway_url: String,
    pub request_timeout_secs: u64,
    pub max_retries: u32,
    pub retry_delay_ms: u64,
}

/// 数据配置
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct DataConfig {
    pub pools_file_path: String,
    pub cache_ttl_secs: u64,
    pub sync_interval_secs: u64,
    pub price_update_threshold: Decimal,
}

/// 执行配置
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct ExecutionConfig {
    pub max_concurrent_trades: usize,
    pub trade_timeout_secs: u64,
    pub gas_price_gwei: u64,
    pub gas_limit: u64,
    pub wallet_private_key: Option<String>,
}

/// 监控配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MonitoringConfig {
    pub log_level: String,
    pub metrics_port: u16,
    pub alert_webhook_url: Option<String>,
    pub performance_log_interval_secs: u64,
}

/// 风险管理配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskConfig {
    pub max_position_size: Decimal,
    pub max_daily_loss: Decimal,
    pub stop_loss_percentage: Decimal,
    pub emergency_stop_enabled: bool,
    pub blacklisted_tokens: Vec<String>,
    pub whitelisted_pools: Vec<String>,
}

impl Settings {
    /// 从文件加载配置
    pub fn from_file(path: &str) -> Result<Self> {
        let content = std::fs::read_to_string(path)
            .map_err(|e| ArbitrageError::ConfigError(format!("无法读取配置文件 {}: {}", path, e)))?;
        
        let settings: Settings = serde_json::from_str(&content)
            .map_err(|e| ArbitrageError::ConfigError(format!("配置文件格式错误: {}", e)))?;
        
        settings.validate()?;
        Ok(settings)
    }
    
    /// 从环境变量加载配置
    pub fn from_env() -> Result<Self> {
        let settings = Settings {
            network: NetworkConfig {
                ic_gateway_url: std::env::var("IC_GATEWAY_URL")
                    .unwrap_or_else(|_| "https://ic0.app".to_string()),
                request_timeout_secs: std::env::var("REQUEST_TIMEOUT_SECS")
                    .unwrap_or_else(|_| "30".to_string())
                    .parse()
                    .map_err(|e| ArbitrageError::ConfigError(format!("无效的请求超时配置: {}", e)))?,
                max_retries: std::env::var("MAX_RETRIES")
                    .unwrap_or_else(|_| "3".to_string())
                    .parse()
                    .map_err(|e| ArbitrageError::ConfigError(format!("无效的重试次数配置: {}", e)))?,
                retry_delay_ms: std::env::var("RETRY_DELAY_MS")
                    .unwrap_or_else(|_| "1000".to_string())
                    .parse()
                    .map_err(|e| ArbitrageError::ConfigError(format!("无效的重试延迟配置: {}", e)))?,
            },
            arbitrage: ArbitrageConfig::default(),
            data: DataConfig {
                pools_file_path: std::env::var("POOLS_FILE_PATH")
                    .unwrap_or_else(|_| "data/pools.json".to_string()),
                cache_ttl_secs: std::env::var("CACHE_TTL_SECS")
                    .unwrap_or_else(|_| "300".to_string())
                    .parse()
                    .map_err(|e| ArbitrageError::ConfigError(format!("无效的缓存TTL配置: {}", e)))?,
                sync_interval_secs: std::env::var("SYNC_INTERVAL_SECS")
                    .unwrap_or_else(|_| "60".to_string())
                    .parse()
                    .map_err(|e| ArbitrageError::ConfigError(format!("无效的同步间隔配置: {}", e)))?,
                price_update_threshold: Decimal::from_f64(
                    std::env::var("PRICE_UPDATE_THRESHOLD")
                        .unwrap_or_else(|_| "0.01".to_string())
                        .parse()
                        .map_err(|e| ArbitrageError::ConfigError(format!("无效的价格更新阈值配置: {}", e)))?
                ).ok_or_else(|| ArbitrageError::ConfigError("无效的价格更新阈值".to_string()))?,
            },
            execution: ExecutionConfig {
                max_concurrent_trades: std::env::var("MAX_CONCURRENT_TRADES")
                    .unwrap_or_else(|_| "5".to_string())
                    .parse()
                    .map_err(|e| ArbitrageError::ConfigError(format!("无效的最大并发交易数配置: {}", e)))?,
                trade_timeout_secs: std::env::var("TRADE_TIMEOUT_SECS")
                    .unwrap_or_else(|_| "300".to_string())
                    .parse()
                    .map_err(|e| ArbitrageError::ConfigError(format!("无效的交易超时配置: {}", e)))?,
                gas_price_gwei: std::env::var("GAS_PRICE_GWEI")
                    .unwrap_or_else(|_| "20".to_string())
                    .parse()
                    .map_err(|e| ArbitrageError::ConfigError(format!("无效的Gas价格配置: {}", e)))?,
                gas_limit: std::env::var("GAS_LIMIT")
                    .unwrap_or_else(|_| "500000".to_string())
                    .parse()
                    .map_err(|e| ArbitrageError::ConfigError(format!("无效的Gas限制配置: {}", e)))?,
                wallet_private_key: std::env::var("WALLET_PRIVATE_KEY").ok(),
            },
            monitoring: MonitoringConfig {
                log_level: std::env::var("LOG_LEVEL")
                    .unwrap_or_else(|_| "info".to_string()),
                metrics_port: std::env::var("METRICS_PORT")
                    .unwrap_or_else(|_| "8080".to_string())
                    .parse()
                    .map_err(|e| ArbitrageError::ConfigError(format!("无效的指标端口配置: {}", e)))?,
                alert_webhook_url: std::env::var("ALERT_WEBHOOK_URL").ok(),
                performance_log_interval_secs: std::env::var("PERFORMANCE_LOG_INTERVAL_SECS")
                    .unwrap_or_else(|_| "300".to_string())
                    .parse()
                    .map_err(|e| ArbitrageError::ConfigError(format!("无效的性能日志间隔配置: {}", e)))?,
            },
            risk: RiskConfig {
                max_position_size: Decimal::from_f64(
                    std::env::var("MAX_POSITION_SIZE")
                        .unwrap_or_else(|_| "100000.0".to_string())
                        .parse()
                        .map_err(|e| ArbitrageError::ConfigError(format!("无效的最大仓位配置: {}", e)))?
                ).ok_or_else(|| ArbitrageError::ConfigError("无效的最大仓位".to_string()))?,
                max_daily_loss: Decimal::from_f64(
                    std::env::var("MAX_DAILY_LOSS")
                        .unwrap_or_else(|_| "1000.0".to_string())
                        .parse()
                        .map_err(|e| ArbitrageError::ConfigError(format!("无效的最大日损失配置: {}", e)))?
                ).ok_or_else(|| ArbitrageError::ConfigError("无效的最大日损失".to_string()))?,
                stop_loss_percentage: Decimal::from_f64(
                    std::env::var("STOP_LOSS_PERCENTAGE")
                        .unwrap_or_else(|_| "0.05".to_string())
                        .parse()
                        .map_err(|e| ArbitrageError::ConfigError(format!("无效的止损百分比配置: {}", e)))?
                ).ok_or_else(|| ArbitrageError::ConfigError("无效的止损百分比".to_string()))?,
                emergency_stop_enabled: std::env::var("EMERGENCY_STOP_ENABLED")
                    .unwrap_or_else(|_| "true".to_string())
                    .parse()
                    .map_err(|e| ArbitrageError::ConfigError(format!("无效的紧急停止配置: {}", e)))?,
                blacklisted_tokens: std::env::var("BLACKLISTED_TOKENS")
                    .unwrap_or_else(|_| "".to_string())
                    .split(',')
                    .filter(|s| !s.is_empty())
                    .map(|s| s.trim().to_string())
                    .collect(),
                whitelisted_pools: std::env::var("WHITELISTED_POOLS")
                    .unwrap_or_else(|_| "".to_string())
                    .split(',')
                    .filter(|s| !s.is_empty())
                    .map(|s| s.trim().to_string())
                    .collect(),
            },
        };
        
        settings.validate()?;
        Ok(settings)
    }
    
    /// 验证配置的有效性
    pub fn validate(&self) -> Result<()> {
        // 验证网络配置
        if self.network.request_timeout_secs == 0 {
            return Err(ArbitrageError::ConfigError("请求超时不能为0".to_string()));
        }
        
        // 验证套利配置
        if self.arbitrage.min_profit_threshold <= Decimal::ZERO {
            return Err(ArbitrageError::ConfigError("最小利润阈值必须大于0".to_string()));
        }
        
        if self.arbitrage.max_trade_amount <= Decimal::ZERO {
            return Err(ArbitrageError::ConfigError("最大交易金额必须大于0".to_string()));
        }
        
        // 验证风险配置
        if self.risk.max_position_size <= Decimal::ZERO {
            return Err(ArbitrageError::ConfigError("最大仓位必须大于0".to_string()));
        }
        
        Ok(())
    }
    
    /// 获取默认配置
    pub fn default() -> Self {
        Self {
            network: NetworkConfig {
                ic_gateway_url: "https://ic0.app".to_string(),
                request_timeout_secs: 30,
                max_retries: 3,
                retry_delay_ms: 1000,
            },
            arbitrage: ArbitrageConfig::default(),
            data: DataConfig {
                pools_file_path: "data/pools.json".to_string(),
                cache_ttl_secs: 300,
                sync_interval_secs: 60,
                price_update_threshold: Decimal::from_f64(0.01).unwrap(),
            },
            execution: ExecutionConfig {
                max_concurrent_trades: 5,
                trade_timeout_secs: 300,
                gas_price_gwei: 20,
                gas_limit: 500000,
                wallet_private_key: None,
            },
            monitoring: MonitoringConfig {
                log_level: "info".to_string(),
                metrics_port: 8080,
                alert_webhook_url: None,
                performance_log_interval_secs: 300,
            },
            risk: RiskConfig {
                max_position_size: Decimal::from(100000),
                max_daily_loss: Decimal::from(1000),
                stop_loss_percentage: Decimal::from_f64(0.05).unwrap(),
                emergency_stop_enabled: true,
                blacklisted_tokens: Vec::new(),
                whitelisted_pools: Vec::new(),
            },
        }
    }
}
