use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::fs;
use anyhow::{Result, Context};
use candid::Principal;
use log::{info, warn, error};

/// 池子配置中的Token信息
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ConfigToken {
    pub symbol: String,
    pub address: String,
    pub standard: String,
    pub decimals: u8,
}

/// 单个池子配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PoolConfig {
    pub id: String,
    pub canister_id: String,
    pub token0: ConfigToken,
    pub token1: ConfigToken,
    pub fee: u32,
    pub enabled: bool,
}

/// 池子配置文件结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PoolsConfig {
    pub pools: Vec<PoolConfig>,
}

/// 池子配置管理器
pub struct PoolConfigManager {
    config: PoolsConfig,
    config_path: String,
}

impl PoolConfigManager {
    /// 从文件加载配置
    pub fn load_from_file(config_path: &str) -> Result<Self> {
        info!("加载池子配置文件: {}", config_path);
        
        let config_content = fs::read_to_string(config_path)
            .with_context(|| format!("无法读取配置文件: {}", config_path))?;
        
        let config: PoolsConfig = serde_json::from_str(&config_content)
            .with_context(|| "解析配置文件失败")?;
        
        info!("成功加载 {} 个池子配置", config.pools.len());
        
        Ok(Self {
            config,
            config_path: config_path.to_string(),
        })
    }
    
    /// 获取所有启用的池子
    pub fn get_enabled_pools(&self) -> Vec<&PoolConfig> {
        self.config.pools
            .iter()
            .filter(|pool| pool.enabled)
            .collect()
    }
    
    /// 获取池子配置映射（池子ID -> Canister Principal）
    pub fn get_pool_canister_mapping(&self) -> Result<HashMap<String, Principal>> {
        let mut mapping = HashMap::new();
        
        for pool in self.get_enabled_pools() {
            let principal = Principal::from_text(&pool.canister_id)
                .with_context(|| format!("无效的Canister ID: {}", pool.canister_id))?;
            
            mapping.insert(pool.id.clone(), principal);
        }
        
        Ok(mapping)
    }
    
    /// 根据池子ID获取配置
    pub fn get_pool_by_id(&self, pool_id: &str) -> Option<&PoolConfig> {
        self.config.pools
            .iter()
            .find(|pool| pool.id == pool_id)
    }
    
    /// 获取所有Token地址映射
    pub fn get_token_mapping(&self) -> HashMap<String, ConfigToken> {
        let mut tokens = HashMap::new();
        
        for pool in &self.config.pools {
            tokens.insert(pool.token0.address.clone(), pool.token0.clone());
            tokens.insert(pool.token1.address.clone(), pool.token1.clone());
        }
        
        tokens
    }
    
    /// 获取包含指定Token的所有池子
    pub fn get_pools_with_token(&self, token_address: &str) -> Vec<&PoolConfig> {
        self.config.pools
            .iter()
            .filter(|pool| {
                pool.enabled && 
                (pool.token0.address == token_address || pool.token1.address == token_address)
            })
            .collect()
    }
    
    /// 查找两个Token之间的直接池子
    pub fn find_direct_pool(&self, token0: &str, token1: &str) -> Option<&PoolConfig> {
        self.config.pools
            .iter()
            .find(|pool| {
                pool.enabled && 
                ((pool.token0.address == token0 && pool.token1.address == token1) ||
                 (pool.token0.address == token1 && pool.token1.address == token0))
            })
    }
    
    /// 重新加载配置文件
    pub fn reload(&mut self) -> Result<()> {
        info!("重新加载池子配置文件");
        
        let new_manager = Self::load_from_file(&self.config_path)?;
        self.config = new_manager.config;
        
        info!("配置文件重新加载成功");
        Ok(())
    }
    
    /// 验证配置的有效性
    pub fn validate_config(&self) -> Result<()> {
        let mut errors = Vec::new();
        
        for pool in &self.config.pools {
            // 验证Canister ID
            if Principal::from_text(&pool.canister_id).is_err() {
                errors.push(format!("池子 {} 的Canister ID无效: {}", pool.id, pool.canister_id));
            }
            
            // 验证Token地址
            if Principal::from_text(&pool.token0.address).is_err() {
                errors.push(format!("池子 {} 的token0地址无效: {}", pool.id, pool.token0.address));
            }
            
            if Principal::from_text(&pool.token1.address).is_err() {
                errors.push(format!("池子 {} 的token1地址无效: {}", pool.id, pool.token1.address));
            }
            
            // 验证手续费
            if pool.fee == 0 || pool.fee > 100000 {
                errors.push(format!("池子 {} 的手续费无效: {}", pool.id, pool.fee));
            }
        }
        
        if !errors.is_empty() {
            error!("配置验证失败:");
            for error in &errors {
                error!("  - {}", error);
            }
            return Err(anyhow::anyhow!("配置验证失败: {} 个错误", errors.len()));
        }
        
        info!("配置验证通过");
        Ok(())
    }
    
    /// 获取配置统计信息
    pub fn get_stats(&self) -> PoolConfigStats {
        let total_pools = self.config.pools.len();
        let enabled_pools = self.get_enabled_pools().len();
        let unique_tokens = self.get_token_mapping().len();
        
        PoolConfigStats {
            total_pools,
            enabled_pools,
            disabled_pools: total_pools - enabled_pools,
            unique_tokens,
        }
    }
}

/// 配置统计信息
#[derive(Debug, Clone)]
pub struct PoolConfigStats {
    pub total_pools: usize,
    pub enabled_pools: usize,
    pub disabled_pools: usize,
    pub unique_tokens: usize,
}

impl std::fmt::Display for PoolConfigStats {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, 
            "池子统计: 总计 {}, 启用 {}, 禁用 {}, 唯一Token {}",
            self.total_pools,
            self.enabled_pools, 
            self.disabled_pools,
            self.unique_tokens
        )
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::fs;
    use tempfile::NamedTempFile;
    
    #[test]
    fn test_load_config() {
        let config_content = r#"
        {
            "pools": [
                {
                    "id": "test-pool",
                    "canister_id": "rdmx6-jaaaa-aaaah-qcaiq-cai",
                    "token0": {
                        "symbol": "ICP",
                        "address": "rrkah-fqaaa-aaaaa-aaaaq-cai",
                        "standard": "ICRC1",
                        "decimals": 8
                    },
                    "token1": {
                        "symbol": "ckBTC",
                        "address": "mxzaz-hqaaa-aaaar-qaada-cai",
                        "standard": "ICRC1",
                        "decimals": 8
                    },
                    "fee": 3000,
                    "enabled": true
                }
            ]
        }
        "#;
        
        let mut temp_file = NamedTempFile::new().unwrap();
        fs::write(temp_file.path(), config_content).unwrap();
        
        let manager = PoolConfigManager::load_from_file(temp_file.path().to_str().unwrap()).unwrap();
        assert_eq!(manager.config.pools.len(), 1);
        assert_eq!(manager.get_enabled_pools().len(), 1);
    }
}
