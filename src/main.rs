use icpswap_arb::{SimpleArbitrageDetector, PoolState, PoolMetadata, Token};
use log::{info, error};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 初始化日志
    env_logger::init();

    info!("启动ICPSwap V3套利系统（简化版本）...");

    // 创建套利检测器
    let mut detector = SimpleArbitrageDetector::new();

    // 添加模拟池子数据
    add_mock_pools(&mut detector);

    info!("开始检测套利机会...");

    // 检测套利机会
    match detector.detect_arbitrage_opportunities() {
        Ok(opportunities) => {
            if !opportunities.is_empty() {
                info!("发现 {} 个套利机会:", opportunities.len());
                for (i, opp) in opportunities.iter().enumerate() {
                    info!("  {}. ID: {}", i + 1, opp.id);
                    info!("     路径: {}", format_token_path(&opp.tokens));
                    info!("     输入金额: {:.4}", opp.input_amount);
                    info!("     预期输出: {:.4}", opp.expected_output);
                    info!("     利润: {:.4} ({:.2}%)", opp.profit, opp.profit_percentage * 100.0);
                    info!("     池子: {:?}", opp.pools);
                    info!("");
                }
            } else {
                info!("未发现套利机会");
            }
        }
        Err(e) => {
            error!("套利检测失败: {}", e);
        }
    }

    info!("套利检测完成");
    Ok(())
}

/// 添加模拟池子数据
fn add_mock_pools(detector: &mut SimpleArbitrageDetector) {
    // 创建模拟代币
    let icp = Token {
        symbol: "ICP".to_string(),
        decimals: 8,
        address: "rrkah-fqaaa-aaaaa-aaaaq-cai".to_string(),
    };

    let ckbtc = Token {
        symbol: "ckBTC".to_string(),
        decimals: 8,
        address: "mxzaz-hqaaa-aaaar-qaada-cai".to_string(),
    };

    let cketh = Token {
        symbol: "ckETH".to_string(),
        decimals: 18,
        address: "ss2fx-dyaaa-aaaar-qacoq-cai".to_string(),
    };

    // 创建模拟池子
    // ICP/ckBTC池子 (价格: 1 ICP = 0.0003 ckBTC)
    let pool1 = PoolState {
        metadata: PoolMetadata {
            key: "icp_ckbtc".to_string(),
            token0: icp.clone(),
            token1: ckbtc.clone(),
            fee: 3000, // 0.3%
            tick: -69000,
            liquidity: 1000000000,
            sqrt_price_x96: 1000000000000000000000000, // 模拟值
        },
        price: 0.0003, // 1 ICP = 0.0003 ckBTC
        last_updated: 1234567890,
        pool_id: "pool1".to_string(),
    };


    // ckBTC/ckETH池子 (价格: 1 ckBTC = 15 ckETH)
    let pool2 = PoolState {
        metadata: PoolMetadata {
            key: "ckbtc_cketh".to_string(),
            token0: ckbtc.clone(),
            token1: cketh.clone(),
            fee: 3000, // 0.3%
            tick: 27000,
            liquidity: 500000000,
            sqrt_price_x96: 5000000000000000000000000, // 模拟值
        },
        price: 15.0, // 1 ckBTC = 15 ckETH
        last_updated: 1234567890,
        pool_id: "pool2".to_string(),
    };

    // ICP/ckETH池子 (价格: 1 ICP = 0.005 ckETH，创造套利机会)
    let pool3 = PoolState {
        metadata: PoolMetadata {
            key: "icp_cketh".to_string(),
            token0: icp.clone(),
            token1: cketh.clone(),
            fee: 3000, // 0.3%
            tick: -46000,
            liquidity: 800000000,
            sqrt_price_x96: 2000000000000000000000000, // 模拟值
        },
        price: 0.005, // 1 ICP = 0.005 ckETH (理论上应该是 0.0003 * 15 = 0.0045)
        last_updated: 1234567890,
        pool_id: "pool3".to_string(),
    };

    // 添加池子到检测器
    detector.add_pool(pool1);
    detector.add_pool(pool2);
    detector.add_pool(pool3);

    info!("已添加 3 个模拟池子:");
    info!("  1. ICP/ckBTC (价格: 0.0003)");
    info!("  2. ckBTC/ckETH (价格: 15.0)");
    info!("  3. ICP/ckETH (价格: 0.005)");
    info!("  理论套利路径: ICP -> ckBTC -> ckETH -> ICP");
}

/// 格式化代币路径
fn format_token_path(tokens: &[Token]) -> String {
    tokens.iter()
        .map(|token| token.symbol.as_str())
        .collect::<Vec<_>>()
        .join(" -> ")
}
