use std::sync::Arc;
use tokio::time::{Duration, interval};
use tokio::signal;
use serde_json::json;

use icpswap_arb::{
    config::Settings,
    data::PoolManager,
    algorithms::ArbitrageDetector,
    execution::{TradeExecutor, RiskManager},
    monitoring::{StructuredLogger, MetricsCollector},
    types::{Result, ArbitrageError},
};

/// ICP Swap 套利系统主程序
#[tokio::main]
async fn main() -> Result<()> {
    // 加载配置
    let settings = load_configuration().await?;

    // 初始化日志系统
    let logger = StructuredLogger::new(settings.monitoring.clone());
    logger.initialize()?;

    // 记录系统启动
    let config_summary = json!({
        "arbitrage": {
            "min_profit_threshold": settings.arbitrage.min_profit_threshold.to_string(),
            "max_trade_amount": settings.arbitrage.max_trade_amount.to_string(),
            "confidence_threshold": settings.arbitrage.confidence_threshold
        },
        "execution": {
            "max_concurrent_trades": settings.execution.max_concurrent_trades,
            "trade_timeout_secs": settings.execution.trade_timeout_secs
        },
        "risk": {
            "max_position_size": settings.risk.max_position_size.to_string(),
            "emergency_stop_enabled": settings.risk.emergency_stop_enabled
        }
    });
    logger.log_system_startup(config_summary).await;

    // 初始化指标收集器
    let metrics_collector = Arc::new(MetricsCollector::new());
    metrics_collector.start_collection(settings.monitoring.performance_log_interval_secs).await;

    // 初始化池子管理器
    let pool_manager = PoolManager::new(settings.clone());
    pool_manager.initialize().await?;

    // 初始化风险管理器
    let risk_manager = Arc::new(RiskManager::new(settings.risk.clone()));

    // 初始化交易执行器
    let trade_executor = TradeExecutor::new(settings.execution.clone(), Arc::clone(&risk_manager));

    // 初始化套利检测器
    let arbitrage_detector = ArbitrageDetector::new(settings.arbitrage.clone(), pool_manager);

    // 创建系统实例
    let arbitrage_system = ArbitrageSystem {
        settings,
        logger: Arc::new(logger),
        metrics_collector,
        arbitrage_detector,
        trade_executor,
        risk_manager,
    };

    // 启动系统
    arbitrage_system.run().await
}

/// 套利系统主结构
struct ArbitrageSystem {
    settings: Settings,
    logger: Arc<StructuredLogger>,
    metrics_collector: Arc<MetricsCollector>,
    arbitrage_detector: ArbitrageDetector,
    trade_executor: TradeExecutor,
    risk_manager: Arc<RiskManager>,
}

impl ArbitrageSystem {
    /// 运行套利系统
    async fn run(self) -> Result<()> {
        log::info!("ICP Swap 套利系统启动中...");

        // 设置优雅关闭处理
        self.setup_shutdown_handler().await;

        // 启动主循环
        let main_loop = self.main_loop();

        // 等待关闭信号或主循环结束
        tokio::select! {
            result = main_loop => {
                match result {
                    Ok(_) => log::info!("主循环正常结束"),
                    Err(e) => {
                        log::error!("主循环异常结束: {}", e);
                        self.logger.log_error(&e, Some("main_loop")).await;
                    }
                }
            }
            _ = signal::ctrl_c() => {
                log::info!("收到关闭信号，正在优雅关闭...");
            }
        }

        // 执行清理工作
        self.cleanup().await?;

        self.logger.log_system_shutdown("正常关闭").await;
        log::info!("ICP Swap 套利系统已关闭");

        Ok(())
    }

    /// 主循环
    async fn main_loop(&self) -> Result<()> {
        let mut detection_interval = interval(Duration::from_secs(
            self.settings.data.sync_interval_secs
        ));

        let mut metrics_interval = interval(Duration::from_secs(
            self.settings.monitoring.performance_log_interval_secs
        ));

        loop {
            tokio::select! {
                _ = detection_interval.tick() => {
                    if let Err(e) = self.detection_cycle().await {
                        log::error!("检测周期错误: {}", e);
                        self.logger.log_error(&e, Some("detection_cycle")).await;
                    }
                }
                _ = metrics_interval.tick() => {
                    if let Err(e) = self.metrics_cycle().await {
                        log::error!("指标收集错误: {}", e);
                        self.logger.log_error(&e, Some("metrics_cycle")).await;
                    }
                }
            }
        }
    }

    /// 检测周期
    async fn detection_cycle(&self) -> Result<()> {
        let start_time = std::time::Instant::now();

        // 检查紧急停止状态
        if self.risk_manager.is_emergency_stopped().await {
            log::warn!("系统处于紧急停止状态，跳过检测周期");
            return Ok(());
        }

        log::debug!("开始套利机会检测周期");

        // 检测套利机会
        let opportunities = self.arbitrage_detector.detect_opportunities().await?;

        let detection_latency = start_time.elapsed().as_millis() as u64;
        self.metrics_collector.record_detection_latency(detection_latency).await;

        log::info!("检测到 {} 个套利机会，耗时 {}ms", opportunities.len(), detection_latency);

        // 记录机会到指标
        for opportunity in &opportunities {
            self.metrics_collector.record_opportunity(opportunity).await;
            self.logger.log_opportunity_detected(opportunity).await;
        }

        // 执行套利交易
        if !opportunities.is_empty() {
            self.execute_opportunities(opportunities).await?;
        }

        Ok(())
    }

    /// 执行套利机会
    async fn execute_opportunities(&self, opportunities: Vec<icpswap_arb::types::ArbitrageOpportunity>) -> Result<()> {
        log::info!("开始执行 {} 个套利机会", opportunities.len());

        let execution_start = std::time::Instant::now();

        // 批量执行交易
        let results = self.trade_executor.execute_batch(opportunities).await?;

        let execution_latency = execution_start.elapsed().as_millis() as u64;
        self.metrics_collector.record_execution_latency(execution_latency).await;

        // 处理执行结果
        for result in results {
            self.metrics_collector.record_trade_result(&result).await;
            self.logger.log_trade_execution_result(&result).await;
        }

        log::info!("批量执行完成，耗时 {}ms", execution_latency);

        Ok(())
    }

    /// 指标收集周期
    async fn metrics_cycle(&self) -> Result<()> {
        let performance_metrics = self.metrics_collector.get_performance_metrics().await;
        self.logger.log_performance_metrics(&performance_metrics).await;

        // 更新活跃池子数量
        let pool_stats = self.arbitrage_detector.pool_manager.get_stats().await;
        self.metrics_collector.update_active_pools(pool_stats.active_pools).await;

        Ok(())
    }

    /// 设置关闭信号处理
    async fn setup_shutdown_handler(&self) {
        // 跨平台的信号处理
        #[cfg(unix)]
        {
            use tokio::signal::unix::{signal, SignalKind};

            let mut sigterm = signal(SignalKind::terminate())
                .expect("无法注册SIGTERM信号处理器");

            tokio::spawn(async move {
                sigterm.recv().await;
                log::info!("收到SIGTERM信号");
            });
        }

        // Windows和其他平台使用Ctrl+C
        tokio::spawn(async move {
            signal::ctrl_c().await.expect("无法注册Ctrl+C信号处理器");
            log::info!("收到Ctrl+C信号");
        });
    }

    /// 清理资源
    async fn cleanup(&self) -> Result<()> {
        log::info!("正在清理系统资源...");

        // 等待所有活跃交易完成
        let executor_status = self.trade_executor.get_status();
        if executor_status.active_trades > 0 {
            log::info!("等待 {} 个活跃交易完成...", executor_status.active_trades);

            // 等待最多30秒
            for _ in 0..30 {
                tokio::time::sleep(Duration::from_secs(1)).await;
                let status = self.trade_executor.get_status();
                if status.active_trades == 0 {
                    break;
                }
            }
        }

        // 记录最终统计
        let final_stats = self.metrics_collector.get_arbitrage_stats().await;
        log::info!("最终统计: 总机会数={}, 执行交易数={}, 成功率={:.2}%, 总利润={}",
                  final_stats.total_opportunities,
                  final_stats.executed_trades,
                  final_stats.success_rate * 100.0,
                  final_stats.total_profit);

        Ok(())
    }
}

/// 加载配置
async fn load_configuration() -> Result<Settings> {
    // 首先尝试从环境变量加载
    match Settings::from_env() {
        Ok(settings) => {
            log::info!("从环境变量加载配置成功");
            Ok(settings)
        },
        Err(_) => {
            // 如果环境变量加载失败，尝试从文件加载
            match Settings::from_file("config.json") {
                Ok(settings) => {
                    log::info!("从配置文件加载配置成功");
                    Ok(settings)
                },
                Err(_) => {
                    // 如果都失败，使用默认配置
                    log::warn!("无法加载配置文件，使用默认配置");
                    Ok(Settings::default())
                }
            }
        }
    }
}
