use crate::network::ICPClient;
use crate::types::{RealtimePoolState, RealtimePoolMetadata, Token, Result, ArbitrageError};
use candid::{CandidType, Deserialize, Principal, Nat};
use ic_utils::Canister;
use anyhow::Context;
use std::collections::HashMap;
use log::{info, warn, error, debug};
use rust_decimal::Decimal;
use std::str::FromStr;

/// ICPSwap V3 Token结构（基于真实API）
#[derive(CandidType, Deserialize, Debug, Clone)]
pub struct ICPSwapToken {
    pub address: String,
    pub standard: String,
}

/// ICPSwap V3 Pool Metadata（基于真实API）
#[derive(CandidType, Deserialize, Debug, Clone)]
pub struct ICPSwapPoolMetadata {
    pub fee: Nat,
    pub key: String,
    #[serde(rename = "sqrtPriceX96")]
    pub sqrt_price_x96: Nat,
    pub tick: i32,
    pub liquidity: Nat,
    pub token0: ICPSwapToken,
    pub token1: ICPSwapToken,
    #[serde(rename = "maxLiquidityPerTick")]
    pub max_liquidity_per_tick: Nat,
    #[serde(rename = "nextPositionId")]
    pub next_position_id: Nat,
}

/// ICPSwap V3 API响应结构
#[derive(CandidType, Deserialize, Debug, Clone)]
pub enum ICPSwapResult<T> {
    #[serde(rename = "ok")]
    Ok(T),
    #[serde(rename = "err")]
    Err(ICPSwapError),
}

/// ICPSwap V3 错误类型
#[derive(CandidType, Deserialize, Debug, Clone)]
pub enum ICPSwapError {
    CommonError,
    #[serde(rename = "InternalError")]
    InternalError(String),
    #[serde(rename = "UnsupportedToken")]
    UnsupportedToken(String),
    InsufficientFunds,
}

/// Swap参数
#[derive(CandidType, Deserialize, Debug, Clone)]
pub struct SwapArgs {
    #[serde(rename = "amountIn")]
    pub amount_in: String,
    #[serde(rename = "zeroForOne")]
    pub zero_for_one: bool,
    #[serde(rename = "amountOutMinimum")]
    pub amount_out_minimum: String,
}

/// 数据获取器，负责从ICPSwap V3 Canister获取实时数据
pub struct DataFetcher {
    icp_client: ICPClient,
    pool_canisters: HashMap<String, Principal>,
}

impl DataFetcher {
    /// 创建新的数据获取器
    pub fn new(icp_client: ICPClient) -> Self {
        Self {
            icp_client,
            pool_canisters: HashMap::new(),
        }
    }
    
    /// 添加池子Canister
    pub fn add_pool_canister(&mut self, pool_id: String, canister_id: Principal) {
        info!("添加池子Canister: {} -> {}", pool_id, canister_id);
        self.pool_canisters.insert(pool_id, canister_id);
    }
    
    /// 批量添加池子Canister
    pub fn add_pool_canisters(&mut self, pools: HashMap<String, Principal>) {
        for (pool_id, canister_id) in pools {
            self.add_pool_canister(pool_id, canister_id);
        }
    }
    
    /// 获取单个池子的实时数据
    pub async fn fetch_pool_data(&self, pool_id: &str) -> Result<RealtimePoolState> {
        let canister_id = self.pool_canisters.get(pool_id)
            .ok_or_else(|| ArbitrageError::DataFetchError(
                format!("未找到池子Canister: {}", pool_id)
            ))?;
        
        debug!("获取池子数据: {} ({})", pool_id, canister_id);
        
        // 创建Canister实例
        let canister: Canister<ICPSwapResult<ICPSwapPoolMetadata>> = self.icp_client.create_canister(*canister_id);
        
        // 调用真实的ICPSwap V3 metadata方法
        let metadata_result: ICPSwapResult<ICPSwapPoolMetadata> = self.icp_client.call_with_retry(|| {
            Box::pin(async {
                canister.query("metadata")
                    .build()
                    .call()
                    .await
                    .map_err(|e| anyhow::anyhow!("Canister调用失败: {}", e))
            })
        }).await
        .context("获取池子元数据失败")?;

        // 处理ICPSwap API响应
        let pool_metadata = match metadata_result {
            ICPSwapResult::Ok(metadata) => metadata,
            ICPSwapResult::Err(err) => {
                return Err(ArbitrageError::CanisterError(
                    format!("获取池子元数据失败: {:?}", err)
                ));
            }
        };
        
        // 转换为内部数据结构
        self.convert_metadata_to_state(pool_id, pool_metadata).await
    }
    
    /// 批量获取多个池子的数据
    pub async fn fetch_multiple_pools(&self, pool_ids: &[String]) -> Result<HashMap<String, RealtimePoolState>> {
        let mut results = HashMap::new();
        let mut tasks = Vec::new();
        
        // 创建并发任务
        for pool_id in pool_ids {
            let pool_id = pool_id.clone();
            let fetcher = self.clone_for_async();
            
            let task = tokio::spawn(async move {
                let result = fetcher.fetch_pool_data(&pool_id).await;
                (pool_id, result)
            });
            
            tasks.push(task);
        }
        
        // 等待所有任务完成
        for task in tasks {
            match task.await {
                Ok((pool_id, Ok(pool_state))) => {
                    results.insert(pool_id, pool_state);
                }
                Ok((pool_id, Err(e))) => {
                    warn!("获取池子数据失败 {}: {}", pool_id, e);
                }
                Err(e) => {
                    error!("任务执行失败: {}", e);
                }
            }
        }
        
        info!("成功获取 {}/{} 个池子的数据", results.len(), pool_ids.len());
        Ok(results)
    }
    
    /// 获取所有已配置池子的数据
    pub async fn fetch_all_pools(&self) -> Result<HashMap<String, RealtimePoolState>> {
        let pool_ids: Vec<String> = self.pool_canisters.keys().cloned().collect();
        self.fetch_multiple_pools(&pool_ids).await
    }
    
    /// 获取Token详细信息
    pub async fn fetch_token_info(&self, token_principal: Principal) -> Result<TokenInfo> {
        // 创建Token Canister实例
        let canister: Canister<TokenInfo> = self.icp_client.create_canister(token_principal);
        
        // 调用Token Canister获取信息
        let token_info = self.icp_client.call_with_retry(|| {
            Box::pin(async {
                // 这里需要根据实际的Token Canister接口调整
                canister.query("icrc1_metadata")
                    .build()
                    .call()
                    .await
                    .map_err(|e| anyhow::anyhow!("Token Canister调用失败: {}", e))
            })
        }).await
        .context("获取Token信息失败")?;
        
        Ok(token_info)
    }
    
    /// 检查数据是否有变化
    pub async fn check_data_changes(&self, pool_id: &str, last_state: &RealtimePoolState) -> Result<bool> {
        let current_state = self.fetch_pool_data(pool_id).await?;
        
        // 比较关键数据是否发生变化
        let has_changes = 
            current_state.metadata.sqrt_price_x96 != last_state.metadata.sqrt_price_x96 ||
            current_state.metadata.liquidity != last_state.metadata.liquidity ||
            current_state.metadata.tick != last_state.metadata.tick;
        
        if has_changes {
            debug!("检测到池子数据变化: {}", pool_id);
        }
        
        Ok(has_changes)
    }
    
    /// 转换ICPSwap Metadata到RealtimePoolState
    async fn convert_metadata_to_state(&self, pool_id: &str, metadata: ICPSwapPoolMetadata) -> Result<RealtimePoolState> {
        // 转换sqrt_price_x96
        let sqrt_price_x96 = self.nat_to_u128(&metadata.sqrt_price_x96)?;

        // 转换liquidity
        let liquidity = self.nat_to_u64(&metadata.liquidity)?;

        // 转换fee
        let fee = self.nat_to_u32(&metadata.fee)?;

        // 创建Token结构
        let token0 = Token {
            symbol: self.extract_symbol_from_address(&metadata.token0.address),
            decimals: 8, // 默认值，实际应该从Token Canister获取
            principal: Some(Principal::from_text(&metadata.token0.address)
                .map_err(|e| ArbitrageError::DataConversionError(
                    format!("无效的token0地址: {}", e)
                ))?),
        };

        let token1 = Token {
            symbol: self.extract_symbol_from_address(&metadata.token1.address),
            decimals: 8, // 默认值，实际应该从Token Canister获取
            principal: Some(Principal::from_text(&metadata.token1.address)
                .map_err(|e| ArbitrageError::DataConversionError(
                    format!("无效的token1地址: {}", e)
                ))?),
        };

        // 创建RealtimePoolMetadata
        let pool_metadata = RealtimePoolMetadata {
            sqrt_price_x96,
            liquidity,
            fee,
            tick: metadata.tick,
        };

        Ok(RealtimePoolState {
            pool_id: pool_id.to_string(),
            token0,
            token1,
            metadata: pool_metadata,
        })
    }
    
    /// 转换Nat到u128
    fn nat_to_u128(&self, nat: &Nat) -> Result<u128> {
        let bytes = nat.0.to_bytes_be();
        if bytes.len() > 16 {
            return Err(ArbitrageError::DataConversionError(
                "Nat值太大，无法转换为u128".to_string()
            ));
        }
        
        let mut array = [0u8; 16];
        let start = 16 - bytes.len();
        array[start..].copy_from_slice(&bytes);
        Ok(u128::from_be_bytes(array))
    }
    
    /// 转换Nat到u64
    fn nat_to_u64(&self, nat: &Nat) -> Result<u64> {
        let bytes = nat.0.to_bytes_be();
        if bytes.len() > 8 {
            return Err(ArbitrageError::DataConversionError(
                "Nat值太大，无法转换为u64".to_string()
            ));
        }

        let mut array = [0u8; 8];
        let start = 8 - bytes.len();
        array[start..].copy_from_slice(&bytes);
        Ok(u64::from_be_bytes(array))
    }

    /// 转换Nat到u32
    fn nat_to_u32(&self, nat: &Nat) -> Result<u32> {
        let bytes = nat.0.to_bytes_be();
        if bytes.len() > 4 {
            return Err(ArbitrageError::DataConversionError(
                "Nat值太大，无法转换为u32".to_string()
            ));
        }

        let mut array = [0u8; 4];
        let start = 4 - bytes.len();
        array[start..].copy_from_slice(&bytes);
        Ok(u32::from_be_bytes(array))
    }

    /// 从地址提取Token符号（简化实现）
    fn extract_symbol_from_address(&self, address: &str) -> String {
        // 这是一个简化的实现，实际应该维护一个地址到符号的映射
        match address {
            "rrkah-fqaaa-aaaaa-aaaaq-cai" => "ICP".to_string(),
            "mxzaz-hqaaa-aaaar-qaada-cai" => "ckBTC".to_string(),
            "ss2fx-dyaaa-aaaar-qacoq-cai" => "ckETH".to_string(),
            "xevnm-gaaaa-aaaar-qafnq-cai" => "ckUSDC".to_string(),
            _ => "UNKNOWN".to_string(),
        }
    }
    
    /// 为异步操作克隆自身
    fn clone_for_async(&self) -> Self {
        Self {
            icp_client: self.icp_client.clone(),
            pool_canisters: self.pool_canisters.clone(),
        }
    }
}

// 为DataFetcher实现Clone，以支持并发操作
impl Clone for DataFetcher {
    fn clone(&self) -> Self {
        Self {
            icp_client: self.icp_client.clone(),
            pool_canisters: self.pool_canisters.clone(),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::network::ICPConfig;
    
    #[tokio::test]
    async fn test_data_fetcher_creation() {
        let config = ICPConfig::default();
        let icp_client = ICPClient::new(config).await.unwrap();
        let fetcher = DataFetcher::new(icp_client);
        
        assert_eq!(fetcher.pool_canisters.len(), 0);
    }
    
    #[test]
    fn test_nat_conversion() {
        let config = ICPConfig::default();
        // 这里需要实际的ICP客户端来测试，暂时跳过
    }
}
