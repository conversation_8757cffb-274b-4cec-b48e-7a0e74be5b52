use crate::types::{RealtimePoolState, Result, ArbitrageError};
use dashmap::DashMap;
use parking_lot::RwLock;
use std::sync::Arc;
use std::time::{Duration, Instant, SystemTime, UNIX_EPOCH};
use log::{debug, info, warn};
use serde::{Serialize, Deserialize};

/// 缓存条目
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CacheEntry<T> {
    pub data: T,
    pub timestamp: u64,
    pub last_updated: Instant,
    pub access_count: u64,
}

impl<T> CacheEntry<T> {
    pub fn new(data: T) -> Self {
        Self {
            data,
            timestamp: SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap()
                .as_secs(),
            last_updated: Instant::now(),
            access_count: 0,
        }
    }
    
    pub fn is_expired(&self, ttl: Duration) -> bool {
        self.last_updated.elapsed() > ttl
    }
    
    pub fn access(&mut self) {
        self.access_count += 1;
    }
}

/// 缓存配置
#[derive(Debug, <PERSON><PERSON>)]
pub struct CacheConfig {
    /// 缓存生存时间
    pub ttl: Duration,
    /// 最大缓存条目数
    pub max_entries: usize,
    /// 清理间隔
    pub cleanup_interval: Duration,
    /// 是否启用LRU淘汰
    pub enable_lru: bool,
}

impl Default for CacheConfig {
    fn default() -> Self {
        Self {
            ttl: Duration::from_secs(30), // 30秒TTL
            max_entries: 1000,
            cleanup_interval: Duration::from_secs(60), // 1分钟清理一次
            enable_lru: true,
        }
    }
}

/// 缓存统计信息
#[derive(Debug, Clone, Default)]
pub struct CacheStats {
    pub hits: u64,
    pub misses: u64,
    pub evictions: u64,
    pub entries_count: usize,
}

impl CacheStats {
    pub fn hit_rate(&self) -> f64 {
        if self.hits + self.misses == 0 {
            0.0
        } else {
            self.hits as f64 / (self.hits + self.misses) as f64
        }
    }
}

/// 缓存管理器，提供高性能的数据缓存功能
pub struct CacheManager {
    /// 池子状态缓存
    pool_cache: Arc<DashMap<String, CacheEntry<RealtimePoolState>>>,
    /// 缓存配置
    config: CacheConfig,
    /// 缓存统计
    stats: Arc<RwLock<CacheStats>>,
    /// 清理任务句柄
    cleanup_handle: Option<tokio::task::JoinHandle<()>>,
}

impl CacheManager {
    /// 创建新的缓存管理器
    pub fn new(config: CacheConfig) -> Self {
        let cache_manager = Self {
            pool_cache: Arc::new(DashMap::new()),
            config: config.clone(),
            stats: Arc::new(RwLock::new(CacheStats::default())),
            cleanup_handle: None,
        };
        
        cache_manager
    }
    
    /// 启动缓存管理器（包括后台清理任务）
    pub fn start(&mut self) {
        info!("启动缓存管理器，TTL: {:?}, 最大条目: {}", 
              self.config.ttl, self.config.max_entries);
        
        // 启动后台清理任务
        let pool_cache = self.pool_cache.clone();
        let stats = self.stats.clone();
        let cleanup_interval = self.config.cleanup_interval;
        let ttl = self.config.ttl;
        
        let handle = tokio::spawn(async move {
            let mut interval = tokio::time::interval(cleanup_interval);
            
            loop {
                interval.tick().await;
                Self::cleanup_expired_entries(&pool_cache, &stats, ttl).await;
            }
        });
        
        self.cleanup_handle = Some(handle);
    }
    
    /// 停止缓存管理器
    pub async fn stop(&mut self) {
        if let Some(handle) = self.cleanup_handle.take() {
            handle.abort();
            info!("缓存管理器已停止");
        }
    }
    
    /// 获取池子状态（优先从缓存）
    pub fn get_pool_state(&self, pool_id: &str) -> Option<RealtimePoolState> {
        if let Some(mut entry) = self.pool_cache.get_mut(pool_id) {
            if !entry.is_expired(self.config.ttl) {
                entry.access();
                self.increment_hits();
                debug!("缓存命中: {}", pool_id);
                return Some(entry.data.clone());
            } else {
                debug!("缓存过期: {}", pool_id);
                drop(entry);
                self.pool_cache.remove(pool_id);
            }
        }
        
        self.increment_misses();
        debug!("缓存未命中: {}", pool_id);
        None
    }
    
    /// 设置池子状态到缓存
    pub fn set_pool_state(&self, pool_id: String, pool_state: RealtimePoolState) {
        // 检查缓存大小限制
        if self.pool_cache.len() >= self.config.max_entries {
            if self.config.enable_lru {
                self.evict_lru_entry();
            } else {
                warn!("缓存已满，无法添加新条目: {}", pool_id);
                return;
            }
        }
        
        let entry = CacheEntry::new(pool_state);
        self.pool_cache.insert(pool_id.clone(), entry);
        
        debug!("缓存已更新: {}", pool_id);
        self.update_entries_count();
    }
    
    /// 批量设置池子状态
    pub fn set_multiple_pool_states(&self, pool_states: std::collections::HashMap<String, RealtimePoolState>) {
        for (pool_id, pool_state) in pool_states {
            self.set_pool_state(pool_id, pool_state);
        }
    }
    
    /// 检查缓存中是否存在且未过期
    pub fn is_cached_and_fresh(&self, pool_id: &str) -> bool {
        if let Some(entry) = self.pool_cache.get(pool_id) {
            !entry.is_expired(self.config.ttl)
        } else {
            false
        }
    }
    
    /// 强制刷新缓存条目
    pub fn invalidate(&self, pool_id: &str) {
        if self.pool_cache.remove(pool_id).is_some() {
            debug!("缓存条目已失效: {}", pool_id);
            self.update_entries_count();
        }
    }
    
    /// 清空所有缓存
    pub fn clear_all(&self) {
        let count = self.pool_cache.len();
        self.pool_cache.clear();
        info!("已清空所有缓存，共 {} 个条目", count);
        self.update_entries_count();
    }
    
    /// 获取缓存统计信息
    pub fn get_stats(&self) -> CacheStats {
        let mut stats = self.stats.read().clone();
        stats.entries_count = self.pool_cache.len();
        stats
    }
    
    /// 获取所有缓存的池子ID
    pub fn get_cached_pool_ids(&self) -> Vec<String> {
        self.pool_cache.iter()
            .filter(|entry| !entry.is_expired(self.config.ttl))
            .map(|entry| entry.key().clone())
            .collect()
    }
    
    /// 淘汰LRU条目
    fn evict_lru_entry(&self) {
        let mut oldest_key = None;
        let mut oldest_time = Instant::now();
        
        for entry in self.pool_cache.iter() {
            if entry.last_updated < oldest_time {
                oldest_time = entry.last_updated;
                oldest_key = Some(entry.key().clone());
            }
        }
        
        if let Some(key) = oldest_key {
            self.pool_cache.remove(&key);
            self.increment_evictions();
            debug!("LRU淘汰缓存条目: {}", key);
        }
    }
    
    /// 清理过期条目（后台任务）
    async fn cleanup_expired_entries(
        cache: &DashMap<String, CacheEntry<RealtimePoolState>>,
        stats: &RwLock<CacheStats>,
        ttl: Duration,
    ) {
        let mut expired_keys = Vec::new();
        
        // 收集过期的键
        for entry in cache.iter() {
            if entry.is_expired(ttl) {
                expired_keys.push(entry.key().clone());
            }
        }
        
        // 删除过期条目
        let mut eviction_count = 0;
        for key in expired_keys {
            if cache.remove(&key).is_some() {
                eviction_count += 1;
            }
        }
        
        if eviction_count > 0 {
            debug!("清理了 {} 个过期缓存条目", eviction_count);
            stats.write().evictions += eviction_count;
        }
    }
    
    /// 增加命中计数
    fn increment_hits(&self) {
        self.stats.write().hits += 1;
    }
    
    /// 增加未命中计数
    fn increment_misses(&self) {
        self.stats.write().misses += 1;
    }
    
    /// 增加淘汰计数
    fn increment_evictions(&self) {
        self.stats.write().evictions += 1;
    }
    
    /// 更新条目计数
    fn update_entries_count(&self) {
        self.stats.write().entries_count = self.pool_cache.len();
    }
}

impl Drop for CacheManager {
    fn drop(&mut self) {
        if let Some(handle) = self.cleanup_handle.take() {
            handle.abort();
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::types::{Token, PoolMetadata};
    use std::time::Duration;
    
    fn create_test_pool_state(pool_id: &str) -> RealtimePoolState {
        RealtimePoolState {
            pool_id: pool_id.to_string(),
            token0: Token {
                symbol: "BTC".to_string(),
                decimals: 8,
                principal: None,
            },
            token1: Token {
                symbol: "ICP".to_string(),
                decimals: 8,
                principal: None,
            },
            metadata: RealtimePoolMetadata {
                sqrt_price_x96: 1000000000000000000000000u128,
                liquidity: 1000000,
                fee: 3000,
                tick: 0,
            },
        }
    }
    
    #[tokio::test]
    async fn test_cache_basic_operations() {
        let config = CacheConfig {
            ttl: Duration::from_secs(1),
            max_entries: 10,
            cleanup_interval: Duration::from_secs(60),
            enable_lru: true,
        };
        
        let cache = CacheManager::new(config);
        let pool_state = create_test_pool_state("test-pool");
        
        // 测试设置和获取
        cache.set_pool_state("test-pool".to_string(), pool_state.clone());
        let cached_state = cache.get_pool_state("test-pool");
        assert!(cached_state.is_some());
        assert_eq!(cached_state.unwrap().pool_id, "test-pool");
        
        // 测试过期
        tokio::time::sleep(Duration::from_secs(2)).await;
        let expired_state = cache.get_pool_state("test-pool");
        assert!(expired_state.is_none());
    }
    
    #[test]
    fn test_cache_stats() {
        let cache = CacheManager::new(CacheConfig::default());
        let pool_state = create_test_pool_state("test-pool");
        
        // 测试未命中
        cache.get_pool_state("nonexistent");
        let stats = cache.get_stats();
        assert_eq!(stats.misses, 1);
        
        // 测试命中
        cache.set_pool_state("test-pool".to_string(), pool_state);
        cache.get_pool_state("test-pool");
        let stats = cache.get_stats();
        assert_eq!(stats.hits, 1);
    }
}
