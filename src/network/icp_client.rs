use ic_agent::{Agent, agent::http_transport::reqwest_transport::ReqwestHttpReplicaV2Transport};
use ic_utils::Canister;
use candid::{CandidType, Deserialize, Principal};
use anyhow::{Result, Context};
use std::sync::Arc;
use url::Url;
use log::{info, warn, error};

/// ICP网络配置
#[derive(Debug, Clone)]
pub struct ICPConfig {
    /// ICP网络URL (主网或测试网)
    pub network_url: String,
    /// 是否使用主网 (true: 主网, false: 测试网)
    pub use_mainnet: bool,
    /// 请求超时时间 (秒)
    pub timeout_secs: u64,
    /// 重试次数
    pub max_retries: u32,
}

impl Default for ICPConfig {
    fn default() -> Self {
        Self {
            network_url: "https://ic0.app".to_string(), // 主网URL
            use_mainnet: true,
            timeout_secs: 30,
            max_retries: 3,
        }
    }
}

/// ICP客户端，负责与ICP网络通信
pub struct ICPClient {
    agent: Arc<Agent>,
    config: ICPConfig,
}

impl ICPClient {
    /// 创建新的ICP客户端
    pub async fn new(config: ICPConfig) -> Result<Self> {
        info!("初始化ICP客户端，网络: {}", config.network_url);
        
        // 解析网络URL
        let url = Url::parse(&config.network_url)
            .context("无效的网络URL")?;
        
        // 创建HTTP传输
        let transport = ReqwestHttpReplicaV2Transport::create(url)
            .context("创建HTTP传输失败")?;
        
        // 创建Agent
        let agent = Agent::builder()
            .with_transport(transport)
            .build()
            .context("创建Agent失败")?;
        
        // 如果不是主网，获取根密钥（测试网需要）
        if !config.use_mainnet {
            agent.fetch_root_key().await
                .context("获取根密钥失败")?;
        }
        
        info!("ICP客户端初始化成功");
        
        Ok(Self {
            agent: Arc::new(agent),
            config,
        })
    }
    
    /// 创建测试网客户端
    pub async fn new_testnet() -> Result<Self> {
        let config = ICPConfig {
            network_url: "https://testnet.dfinity.network".to_string(),
            use_mainnet: false,
            timeout_secs: 30,
            max_retries: 3,
        };
        
        Self::new(config).await
    }
    
    /// 创建主网客户端
    pub async fn new_mainnet() -> Result<Self> {
        let config = ICPConfig::default();
        Self::new(config).await
    }
    
    /// 获取Agent引用
    pub fn agent(&self) -> Arc<Agent> {
        self.agent.clone()
    }
    
    /// 创建Canister实例
    pub fn create_canister<T>(&self, canister_id: Principal) -> Canister<T>
    where
        T: CandidType + for<'de> Deserialize<'de>,
    {
        Canister::builder()
            .with_agent(&self.agent)
            .with_canister_id(canister_id)
            .build()
            .expect("创建Canister失败")
    }
    
    /// 带重试的异步调用
    pub async fn call_with_retry<F, T, E>(&self, operation: F) -> Result<T>
    where
        F: Fn() -> futures::future::BoxFuture<'_, std::result::Result<T, E>> + Send + Sync,
        E: std::error::Error + Send + Sync + 'static,
        T: Send,
    {
        let mut last_error = None;
        
        for attempt in 1..=self.config.max_retries {
            match operation().await {
                Ok(result) => return Ok(result),
                Err(e) => {
                    warn!("调用失败 (尝试 {}/{}): {}", attempt, self.config.max_retries, e);
                    last_error = Some(e);
                    
                    if attempt < self.config.max_retries {
                        // 指数退避
                        let delay = std::time::Duration::from_millis(100 * (1 << attempt));
                        tokio::time::sleep(delay).await;
                    }
                }
            }
        }
        
        Err(anyhow::anyhow!(
            "调用失败，已重试{}次: {:?}",
            self.config.max_retries,
            last_error
        ))
    }
    
    /// 检查网络连接
    pub async fn check_connection(&self) -> Result<bool> {
        match self.agent.status().await {
            Ok(_) => {
                info!("ICP网络连接正常");
                Ok(true)
            }
            Err(e) => {
                error!("ICP网络连接失败: {}", e);
                Ok(false)
            }
        }
    }
    
    /// 获取网络状态
    pub async fn get_network_status(&self) -> Result<NetworkStatus> {
        let status = self.agent.status().await
            .context("获取网络状态失败")?;
        
        Ok(NetworkStatus {
            replica_health_status: status.replica_health_status.unwrap_or_default(),
            root_key: status.root_key.map(|k| hex::encode(k)),
            impl_version: status.impl_version.unwrap_or_default(),
            impl_revision: status.impl_revision.unwrap_or_default(),
        })
    }
}

/// 网络状态信息
#[derive(Debug, Clone)]
pub struct NetworkStatus {
    pub replica_health_status: String,
    pub root_key: Option<String>,
    pub impl_version: String,
    pub impl_revision: String,
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[tokio::test]
    async fn test_icp_client_creation() {
        let config = ICPConfig {
            network_url: "https://ic0.app".to_string(),
            use_mainnet: false, // 测试时不获取根密钥
            timeout_secs: 10,
            max_retries: 1,
        };
        
        let client = ICPClient::new(config).await;
        assert!(client.is_ok());
    }
    
    #[tokio::test]
    async fn test_testnet_client() {
        let client = ICPClient::new_testnet().await;
        assert!(client.is_ok());
    }
}
