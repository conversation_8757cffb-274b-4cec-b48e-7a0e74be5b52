use crate::network::{<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, CacheConfig};
use crate::types::{RealtimePoolState, Result, ArbitrageError};
use crate::core::ArbitrageEngine;
use std::sync::Arc;
use std::time::Duration;
use std::collections::HashMap;
use tokio::sync::{RwLock, mpsc, broadcast};
use log::{info, warn, error, debug};
use async_trait::async_trait;

/// 数据更新事件
#[derive(Debug, Clone)]
pub enum UpdateEvent {
    /// 池子数据更新
    PoolUpdated {
        pool_id: String,
        old_state: Option<RealtimePoolState>,
        new_state: RealtimePoolState,
    },
    /// 批量池子更新
    BatchPoolsUpdated {
        updated_pools: HashMap<String, RealtimePoolState>,
    },
    /// 更新错误
    UpdateError {
        pool_id: String,
        error: String,
    },
    /// 套利机会检测
    ArbitrageOpportunity {
        opportunity: String, // 这里可以定义具体的套利机会结构
    },
}

/// 数据更新监听器
#[async_trait]
pub trait UpdateListener: Send + Sync {
    async fn on_update(&self, event: UpdateEvent);
}

/// 实时数据更新器配置
#[derive(Debug, Clone)]
pub struct UpdaterConfig {
    /// 更新间隔
    pub update_interval: Duration,
    /// 批量更新大小
    pub batch_size: usize,
    /// 是否启用变化检测
    pub enable_change_detection: bool,
    /// 最大并发更新数
    pub max_concurrent_updates: usize,
    /// 错误重试次数
    pub max_retries: u32,
    /// 重试间隔
    pub retry_interval: Duration,
}

impl Default for UpdaterConfig {
    fn default() -> Self {
        Self {
            update_interval: Duration::from_secs(5), // 5秒更新间隔
            batch_size: 10,
            enable_change_detection: true,
            max_concurrent_updates: 5,
            max_retries: 3,
            retry_interval: Duration::from_secs(1),
        }
    }
}

/// 实时数据更新器
pub struct RealtimeUpdater {
    data_fetcher: Arc<DataFetcher>,
    cache_manager: Arc<CacheManager>,
    arbitrage_engine: Option<Arc<ArbitrageEngine>>,
    config: UpdaterConfig,
    
    /// 要监控的池子列表
    monitored_pools: Arc<RwLock<Vec<String>>>,
    
    /// 事件广播器
    event_sender: broadcast::Sender<UpdateEvent>,
    
    /// 更新任务句柄
    update_handle: Option<tokio::task::JoinHandle<()>>,
    
    /// 停止信号
    stop_sender: Option<mpsc::Sender<()>>,
}

impl RealtimeUpdater {
    /// 创建新的实时更新器
    pub fn new(
        data_fetcher: DataFetcher,
        cache_config: CacheConfig,
        config: UpdaterConfig,
    ) -> Self {
        let mut cache_manager = CacheManager::new(cache_config);
        cache_manager.start();
        
        let (event_sender, _) = broadcast::channel(1000);
        
        Self {
            data_fetcher: Arc::new(data_fetcher),
            cache_manager: Arc::new(cache_manager),
            arbitrage_engine: None,
            config,
            monitored_pools: Arc::new(RwLock::new(Vec::new())),
            event_sender,
            update_handle: None,
            stop_sender: None,
        }
    }
    
    /// 设置套利引擎
    pub fn set_arbitrage_engine(&mut self, engine: Arc<ArbitrageEngine>) {
        self.arbitrage_engine = Some(engine);
    }
    
    /// 添加要监控的池子
    pub async fn add_monitored_pool(&self, pool_id: String) {
        let mut pools = self.monitored_pools.write().await;
        if !pools.contains(&pool_id) {
            pools.push(pool_id.clone());
            info!("添加监控池子: {}", pool_id);
        }
    }
    
    /// 批量添加监控池子
    pub async fn add_monitored_pools(&self, pool_ids: Vec<String>) {
        let mut pools = self.monitored_pools.write().await;
        for pool_id in pool_ids {
            if !pools.contains(&pool_id) {
                pools.push(pool_id.clone());
                info!("添加监控池子: {}", pool_id);
            }
        }
    }
    
    /// 移除监控池子
    pub async fn remove_monitored_pool(&self, pool_id: &str) {
        let mut pools = self.monitored_pools.write().await;
        pools.retain(|p| p != pool_id);
        info!("移除监控池子: {}", pool_id);
    }
    
    /// 启动实时更新
    pub async fn start(&mut self) -> Result<()> {
        if self.update_handle.is_some() {
            return Err(ArbitrageError::SystemError("更新器已经启动".to_string()));
        }
        
        info!("启动实时数据更新器，更新间隔: {:?}", self.config.update_interval);
        
        let (stop_sender, mut stop_receiver) = mpsc::channel(1);
        self.stop_sender = Some(stop_sender);
        
        // 克隆必要的组件
        let data_fetcher = self.data_fetcher.clone();
        let cache_manager = self.cache_manager.clone();
        let arbitrage_engine = self.arbitrage_engine.clone();
        let monitored_pools = self.monitored_pools.clone();
        let event_sender = self.event_sender.clone();
        let config = self.config.clone();
        
        // 启动更新任务
        let handle = tokio::spawn(async move {
            let mut interval = tokio::time::interval(config.update_interval);
            
            loop {
                tokio::select! {
                    _ = interval.tick() => {
                        Self::update_cycle(
                            &data_fetcher,
                            &cache_manager,
                            &arbitrage_engine,
                            &monitored_pools,
                            &event_sender,
                            &config,
                        ).await;
                    }
                    _ = stop_receiver.recv() => {
                        info!("收到停止信号，退出更新循环");
                        break;
                    }
                }
            }
        });
        
        self.update_handle = Some(handle);
        Ok(())
    }
    
    /// 停止实时更新
    pub async fn stop(&mut self) {
        if let Some(sender) = self.stop_sender.take() {
            let _ = sender.send(()).await;
        }
        
        if let Some(handle) = self.update_handle.take() {
            let _ = handle.await;
        }
        
        info!("实时数据更新器已停止");
    }
    
    /// 订阅更新事件
    pub fn subscribe(&self) -> broadcast::Receiver<UpdateEvent> {
        self.event_sender.subscribe()
    }
    
    /// 手动触发更新
    pub async fn trigger_update(&self) -> Result<()> {
        let pools = self.monitored_pools.read().await.clone();
        
        if pools.is_empty() {
            return Ok(());
        }
        
        Self::update_cycle(
            &self.data_fetcher,
            &self.cache_manager,
            &self.arbitrage_engine,
            &self.monitored_pools,
            &self.event_sender,
            &self.config,
        ).await;
        
        Ok(())
    }
    
    /// 获取当前缓存的池子状态
    pub fn get_cached_pool_state(&self, pool_id: &str) -> Option<RealtimePoolState> {
        self.cache_manager.get_pool_state(pool_id)
    }
    
    /// 获取缓存统计信息
    pub fn get_cache_stats(&self) -> crate::network::cache_manager::CacheStats {
        self.cache_manager.get_stats()
    }
    
    /// 更新周期执行
    async fn update_cycle(
        data_fetcher: &DataFetcher,
        cache_manager: &CacheManager,
        arbitrage_engine: &Option<Arc<ArbitrageEngine>>,
        monitored_pools: &RwLock<Vec<String>>,
        event_sender: &broadcast::Sender<UpdateEvent>,
        config: &UpdaterConfig,
    ) {
        let pools = monitored_pools.read().await.clone();
        
        if pools.is_empty() {
            return;
        }
        
        debug!("开始更新周期，监控池子数量: {}", pools.len());
        
        // 分批处理池子
        for chunk in pools.chunks(config.batch_size) {
            Self::update_pool_batch(
                data_fetcher,
                cache_manager,
                arbitrage_engine,
                chunk,
                event_sender,
                config,
            ).await;
        }
    }
    
    /// 批量更新池子数据
    async fn update_pool_batch(
        data_fetcher: &DataFetcher,
        cache_manager: &CacheManager,
        arbitrage_engine: &Option<Arc<ArbitrageEngine>>,
        pool_ids: &[String],
        event_sender: &broadcast::Sender<UpdateEvent>,
        config: &UpdaterConfig,
    ) {
        // 获取新数据
        match data_fetcher.fetch_multiple_pools(pool_ids).await {
            Ok(new_states) => {
                let mut updated_pools = HashMap::new();
                
                for (pool_id, new_state) in new_states {
                    // 检查是否有变化
                    let old_state = cache_manager.get_pool_state(&pool_id);
                    let has_changes = if config.enable_change_detection {
                        match &old_state {
                            Some(old) => Self::has_significant_changes(old, &new_state),
                            None => true,
                        }
                    } else {
                        true
                    };
                    
                    if has_changes {
                        // 更新缓存
                        cache_manager.set_pool_state(pool_id.clone(), new_state.clone());
                        updated_pools.insert(pool_id.clone(), new_state.clone());
                        
                        // 发送更新事件
                        let event = UpdateEvent::PoolUpdated {
                            pool_id: pool_id.clone(),
                            old_state,
                            new_state,
                        };
                        
                        let _ = event_sender.send(event);
                    }
                }
                
                // 如果有更新，触发套利检测
                if !updated_pools.is_empty() {
                    if let Some(engine) = arbitrage_engine {
                        Self::trigger_arbitrage_detection(engine, &updated_pools, event_sender).await;
                    }
                    
                    // 发送批量更新事件
                    let batch_event = UpdateEvent::BatchPoolsUpdated { updated_pools };
                    let _ = event_sender.send(batch_event);
                }
            }
            Err(e) => {
                error!("批量更新池子数据失败: {}", e);
                for pool_id in pool_ids {
                    let event = UpdateEvent::UpdateError {
                        pool_id: pool_id.clone(),
                        error: e.to_string(),
                    };
                    let _ = event_sender.send(event);
                }
            }
        }
    }
    
    /// 检查是否有显著变化
    fn has_significant_changes(old_state: &RealtimePoolState, new_state: &RealtimePoolState) -> bool {
        old_state.metadata.sqrt_price_x96 != new_state.metadata.sqrt_price_x96 ||
        old_state.metadata.liquidity != new_state.metadata.liquidity ||
        old_state.metadata.tick != new_state.metadata.tick
    }
    
    /// 触发套利检测
    async fn trigger_arbitrage_detection(
        engine: &ArbitrageEngine,
        updated_pools: &HashMap<String, RealtimePoolState>,
        event_sender: &broadcast::Sender<UpdateEvent>,
    ) {
        // 这里集成套利检测逻辑
        // 由于ArbitrageEngine的具体实现可能需要调整，这里先预留接口
        debug!("触发套利检测，更新池子数量: {}", updated_pools.len());
        
        // TODO: 实现具体的套利检测逻辑
        // let opportunities = engine.detect_opportunities(updated_pools).await;
        // for opportunity in opportunities {
        //     let event = UpdateEvent::ArbitrageOpportunity { opportunity };
        //     let _ = event_sender.send(event);
        // }
    }
}

impl Drop for RealtimeUpdater {
    fn drop(&mut self) {
        if let Some(handle) = self.update_handle.take() {
            handle.abort();
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::network::{ICPClient, ICPConfig};
    
    #[tokio::test]
    async fn test_realtime_updater_creation() {
        let icp_client = ICPClient::new(ICPConfig::default()).await.unwrap();
        let data_fetcher = DataFetcher::new(icp_client);
        let cache_config = CacheConfig::default();
        let updater_config = UpdaterConfig::default();
        
        let updater = RealtimeUpdater::new(data_fetcher, cache_config, updater_config);
        assert!(updater.update_handle.is_none());
    }
}
