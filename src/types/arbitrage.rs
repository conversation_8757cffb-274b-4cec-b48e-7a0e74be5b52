use serde::{Deserialize, Serialize};
use crate::types::pool::Token;

/// 简化的套利机会
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ArbitrageOpportunity {
    pub id: String,
    pub tokens: Vec<Token>, // 交易路径中的代币
    pub pools: Vec<String>, // 交易路径中的池子ID
    pub input_amount: f64,
    pub expected_output: f64,
    pub profit: f64,
    pub profit_percentage: f64,
    pub timestamp: u64,
}

/// 简化的套利配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ArbitrageConfig {
    pub min_profit_threshold: f64,
    pub min_profit_percentage: f64,
    pub max_trade_amount: f64,
    pub max_path_length: usize,
}

impl ArbitrageOpportunity {
    /// 检查套利机会是否仍然有效
    pub fn is_valid(&self, current_time: u64, validity_window: u64) -> bool {
        current_time - self.timestamp <= validity_window
    }
}

impl ArbitrageConfig {
    /// 默认配置
    pub fn default() -> Self {
        Self {
            min_profit_threshold: 10.0, // 最小10个单位利润
            min_profit_percentage: 0.01, // 1%
            max_trade_amount: 1000000.0, // 最大交易金额
            max_path_length: 4, // 最大路径长度
        }
    }
}
