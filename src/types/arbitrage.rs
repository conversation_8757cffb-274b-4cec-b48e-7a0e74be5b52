use serde::{Deserialize, Serialize};
use rust_decimal::Decimal;
use num_traits::FromPrimitive;
use crate::types::pool::{Token, TradePath};

/// 套利机会
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ArbitrageOpportunity {
    pub id: String,
    pub path: TradePath,
    pub input_amount: Decimal,
    pub expected_output: Decimal,
    pub profit: Decimal,
    pub profit_percentage: Decimal,
    pub gas_cost: Decimal,
    pub net_profit: Decimal,
    pub confidence: f64, // 0.0 - 1.0
    pub timestamp: u64,
}

/// 套利策略配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ArbitrageConfig {
    pub min_profit_threshold: Decimal,
    pub min_profit_percentage: Decimal,
    pub max_trade_amount: Decimal,
    pub max_slippage: Decimal,
    pub max_path_length: usize,
    pub confidence_threshold: f64,
}

/// 套利执行结果
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct ArbitrageResult {
    pub opportunity_id: String,
    pub executed: bool,
    pub actual_profit: Option<Decimal>,
    pub execution_time: u64,
    pub gas_used: Option<u64>,
    pub error: Option<String>,
}

/// 套利统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ArbitrageStats {
    pub total_opportunities: u64,
    pub executed_trades: u64,
    pub total_profit: Decimal,
    pub success_rate: f64,
    pub average_profit: Decimal,
    pub last_updated: u64,
}

/// 图节点（代表代币）
#[derive(Debug, Clone, PartialEq, Eq, Hash)]
pub struct GraphNode {
    pub token: Token,
}

/// 图边（代表交易池）
#[derive(Debug, Clone)]
pub struct GraphEdge {
    pub from: Token,
    pub to: Token,
    pub pool_id: String,
    pub weight: Decimal, // 负对数价格，用于Bellman-Ford算法
    pub fee: u64,
    pub liquidity: u128,
}

/// 交易图
#[derive(Debug)]
pub struct TradingGraph {
    pub nodes: Vec<GraphNode>,
    pub edges: Vec<GraphEdge>,
}

impl ArbitrageOpportunity {
    /// 检查套利机会是否仍然有效
    pub fn is_valid(&self, current_time: u64, validity_window: u64) -> bool {
        current_time - self.timestamp <= validity_window
    }
    
    /// 计算风险调整后的利润
    pub fn risk_adjusted_profit(&self) -> Decimal {
        let confidence_decimal = Decimal::from_f64(self.confidence).unwrap_or(Decimal::ONE);
        self.net_profit * confidence_decimal
    }
}

impl ArbitrageConfig {
    /// 默认配置
    pub fn default() -> Self {
        Self {
            min_profit_threshold: Decimal::from(10), // 最小10个单位利润
            min_profit_percentage: Decimal::from_f64(0.01).unwrap(), // 1%
            max_trade_amount: Decimal::from(1000000), // 最大交易金额
            max_slippage: Decimal::from_f64(0.05).unwrap(), // 5%
            max_path_length: 4, // 最大路径长度
            confidence_threshold: 0.8, // 80%置信度
        }
    }
}

impl TradingGraph {
    pub fn new() -> Self {
        Self {
            nodes: Vec::new(),
            edges: Vec::new(),
        }
    }
    
    /// 添加节点
    pub fn add_node(&mut self, token: Token) {
        let node = GraphNode { token };
        if !self.nodes.contains(&node) {
            self.nodes.push(node);
        }
    }
    
    /// 添加边
    pub fn add_edge(&mut self, edge: GraphEdge) {
        self.edges.push(edge);
    }
    
    /// 获取从指定代币出发的所有边
    pub fn get_edges_from(&self, token: &Token) -> Vec<&GraphEdge> {
        self.edges.iter()
            .filter(|edge| &edge.from == token)
            .collect()
    }
}
