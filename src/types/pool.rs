use serde::{Deserialize, Serialize};
use rust_decimal::Decimal;
use candid::Principal;
use std::collections::HashMap;


/// 代币信息
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, Hash)]
pub struct Token {
    pub symbol: String,
    pub decimals: u8,
    /// ICP Principal (可选，用于ICP网络)
    pub principal: Option<Principal>,
}

/// 交易池元数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PoolMetadata {
    pub key: String,
    pub token0: Token,
    pub token1: Token,
    pub fee: u64,
    pub tick: i32,
    pub liquidity: u128,
    pub sqrt_price_x96: u128,
    pub max_liquidity_per_tick: u128,
    pub next_position_id: u64,
}

/// 交易池状态
#[derive(Debug, <PERSON>lone)]
pub struct PoolState {
    pub metadata: PoolMetadata,
    pub price: Decimal,
    pub inverse_price: Decimal,
    pub last_updated: u64,
    pub canister_id: String,
}

/// 实时池子状态（统一使用这个结构）
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct RealtimePoolState {
    pub pool_id: String,
    pub token0: Token,
    pub token1: Token,
    pub metadata: RealtimePoolMetadata,
}

/// 实时池子元数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RealtimePoolMetadata {
    pub sqrt_price_x96: u128,
    pub liquidity: u64,
    pub fee: u32,
    pub tick: i32,
}

/// 交易对
#[derive(Debug, Clone, PartialEq, Eq, Hash)]
pub struct TradingPair {
    pub token_in: Token,
    pub token_out: Token,
}

/// 价格信息
#[derive(Debug, Clone)]
pub struct PriceInfo {
    pub price: Decimal,
    pub liquidity: u128,
    pub fee: u64,
    pub slippage: Decimal,
}

/// 交易路径
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TradePath {
    pub pools: Vec<String>, // pool canister IDs
    pub tokens: Vec<Token>,
    pub total_fee: u64,
    pub expected_output: Decimal,
}

/// 池子管理器状态
#[derive(Debug)]
pub struct PoolManagerState {
    pub pools: HashMap<String, PoolState>,
    pub token_pairs: HashMap<TradingPair, Vec<String>>, // token pair -> pool IDs
    pub last_sync: u64,
}

impl PoolState {
    /// 计算给定输入金额的输出金额（考虑滑点和手续费）
    pub fn calculate_output(&self, amount_in: Decimal) -> Decimal {
        // 基于UniswapV3的价格计算公式
        // 这里简化实现，实际应该使用更精确的数学库
        let fee_multiplier = Decimal::from(10000 - self.metadata.fee) / Decimal::from(10000);
        let amount_after_fee = amount_in * fee_multiplier;
        
        // 简化的恒定乘积公式，实际应该使用集中流动性公式
        amount_after_fee * self.price
    }
    
    /// 检查池子是否有足够的流动性
    pub fn has_sufficient_liquidity(&self, amount_in: Decimal) -> bool {
        // 简化检查，实际应该基于tick范围内的流动性
        self.metadata.liquidity > 0 && amount_in > Decimal::ZERO
    }
}

impl TradingPair {
    pub fn new(token_in: Token, token_out: Token) -> Self {
        Self { token_in, token_out }
    }
    
    pub fn reverse(&self) -> Self {
        Self {
            token_in: self.token_out.clone(),
            token_out: self.token_in.clone(),
        }
    }
}
