use serde::{Deserialize, Serialize};

/// 代币信息（简化版本）
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, Hash)]
pub struct Token {
    pub symbol: String,
    pub decimals: u8,
    pub address: String, // 简化为字符串地址
}

/// 交易池元数据（统一版本）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PoolMetadata {
    pub key: String,
    pub token0: Token,
    pub token1: Token,
    pub fee: u64,
    pub tick: i32,
    pub liquidity: u128,
    pub sqrt_price_x96: u128,
}

/// 交易池状态（统一版本）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PoolState {
    pub metadata: PoolMetadata,
    pub price: f64, // 简化为f64
    pub last_updated: u64,
    pub pool_id: String,
}

/// 交易对
#[derive(Debug, Clone, PartialEq, Eq, Hash)]
pub struct TradingPair {
    pub token_in: Token,
    pub token_out: Token,
}

/// 交易图节点
#[derive(Debu<PERSON>, <PERSON><PERSON>)]
pub struct GraphNode {
    pub token: Token,
}

/// 交易图边
#[derive(Debug, <PERSON>lone)]
pub struct GraphEdge {
    pub from: Token,
    pub to: Token,
    pub weight: f64, // 负对数价格
    pub pool_id: String,
}

/// 交易图
#[derive(Debug, Clone)]
pub struct TradingGraph {
    pub nodes: Vec<GraphNode>,
    pub edges: Vec<GraphEdge>,
}

impl PoolState {
    /// 计算给定输入金额的输出金额（简化版本）
    pub fn calculate_output(&self, amount_in: f64) -> f64 {
        let fee_multiplier = 1.0 - (self.metadata.fee as f64 / 1_000_000.0);
        let amount_after_fee = amount_in * fee_multiplier;
        amount_after_fee * self.price
    }

    /// 检查池子是否有足够的流动性
    pub fn has_sufficient_liquidity(&self, amount_in: f64) -> bool {
        self.metadata.liquidity > 0 && amount_in > 0.0
    }
}

impl TradingPair {
    pub fn new(token_in: Token, token_out: Token) -> Self {
        Self { token_in, token_out }
    }

    pub fn reverse(&self) -> Self {
        Self {
            token_in: self.token_out.clone(),
            token_out: self.token_in.clone(),
        }
    }
}

impl TradingGraph {
    pub fn new() -> Self {
        Self {
            nodes: Vec::new(),
            edges: Vec::new(),
        }
    }

    pub fn add_node(&mut self, token: Token) {
        if !self.nodes.iter().any(|n| n.token == token) {
            self.nodes.push(GraphNode { token });
        }
    }

    pub fn add_edge(&mut self, from: Token, to: Token, weight: f64, pool_id: String) {
        self.edges.push(GraphEdge { from, to, weight, pool_id });
    }

    pub fn get_edges_from(&self, token: &Token) -> Vec<&GraphEdge> {
        self.edges.iter().filter(|edge| &edge.from == token).collect()
    }
}
