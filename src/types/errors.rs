use thiserror::Error;

/// 系统错误类型定义
#[derive(Error, Debug)]
pub enum ArbitrageError {
    #[error("数据加载错误: {0}")]
    DataLoadError(String),
    
    #[error("价格计算错误: {0}")]
    PriceCalculationError(String),
    
    #[error("套利检测错误: {0}")]
    ArbitrageDetectionError(String),
    
    #[error("交易执行错误: {0}")]
    TradeExecutionError(String),
    
    #[error("网络请求错误: {0}")]
    NetworkError(#[from] reqwest::Error),
    
    #[error("序列化错误: {0}")]
    SerializationError(#[from] serde_json::Error),
    
    #[error("IO错误: {0}")]
    IoError(#[from] std::io::Error),
    
    #[error("配置错误: {0}")]
    ConfigError(String),
    
    #[error("风险管理错误: {0}")]
    RiskManagementError(String),
    
    #[error("内部错误: {0}")]
    InternalError(String),

    #[error("数据获取错误: {0}")]
    DataFetchError(String),

    #[error("数据转换错误: {0}")]
    DataConversionError(String),

    #[error("Canister调用错误: {0}")]
    CanisterError(String),

    #[error("缓存错误: {0}")]
    CacheError(String),

    #[error("系统错误: {0}")]
    SystemError(String),
}

pub type Result<T> = std::result::Result<T, ArbitrageError>;
