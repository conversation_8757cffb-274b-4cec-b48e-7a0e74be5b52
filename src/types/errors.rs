use std::fmt;

/// 简化的错误类型定义
#[derive(Debug, <PERSON><PERSON>)]
pub enum ArbitrageError {
    /// 数据加载错误
    DataLoadError(String),

    /// 价格计算错误
    PriceCalculationError(String),

    /// 套利检测错误
    ArbitrageDetectionError(String),

    /// 内部错误
    InternalError(String),
}

impl fmt::Display for ArbitrageError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            ArbitrageError::DataLoadError(msg) => write!(f, "数据加载错误: {}", msg),
            ArbitrageError::PriceCalculationError(msg) => write!(f, "价格计算错误: {}", msg),
            ArbitrageError::ArbitrageDetectionError(msg) => write!(f, "套利检测错误: {}", msg),
            ArbitrageError::InternalError(msg) => write!(f, "内部错误: {}", msg),
        }
    }
}

impl std::error::Error for ArbitrageError {}

pub type Result<T> = std::result::Result<T, ArbitrageError>;
