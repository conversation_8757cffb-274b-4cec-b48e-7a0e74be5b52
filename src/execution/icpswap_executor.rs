use crate::network::ICPClient;
use crate::types::{Result, ArbitrageError};
use candid::{CandidType, Deserialize, Principal, Nat};
use ic_utils::Canister;
use anyhow::Context;
use log::{info, warn, error, debug};
use std::str::FromStr;

/// ICPSwap V3 Swap参数
#[derive(CandidType, Deserialize, Debug, Clone)]
pub struct SwapArgs {
    #[serde(rename = "amountIn")]
    pub amount_in: String,
    #[serde(rename = "zeroForOne")]
    pub zero_for_one: bool,
    #[serde(rename = "amountOutMinimum")]
    pub amount_out_minimum: String,
}

/// ICPSwap V3 Swap结果
#[derive(CandidType, Deserialize, Debug, Clone)]
pub struct SwapResult {
    #[serde(rename = "amountIn")]
    pub amount_in: Nat,
    #[serde(rename = "amountOut")]
    pub amount_out: Nat,
}

/// ICPSwap V3 API响应
#[derive(CandidType, Deserialize, Debug, Clone)]
pub enum ICPSwapResult<T> {
    #[serde(rename = "ok")]
    Ok(T),
    #[serde(rename = "err")]
    Err(ICPSwapError),
}

/// ICPSwap V3 错误类型
#[derive(CandidType, Deserialize, Debug, Clone)]
pub enum ICPSwapError {
    CommonError,
    #[serde(rename = "InternalError")]
    InternalError(String),
    #[serde(rename = "UnsupportedToken")]
    UnsupportedToken(String),
    InsufficientFunds,
    #[serde(rename = "InsufficientInputAmount")]
    InsufficientInputAmount,
    #[serde(rename = "InsufficientOutputAmount")]
    InsufficientOutputAmount,
    #[serde(rename = "InsufficientLiquidity")]
    InsufficientLiquidity,
}

/// ICRC1 Transfer参数
#[derive(CandidType, Deserialize, Debug, Clone)]
pub struct TransferArgs {
    pub to: Account,
    pub fee: Option<Nat>,
    pub memo: Option<Vec<u8>>,
    pub from_subaccount: Option<Vec<u8>>,
    pub created_at_time: Option<u64>,
    pub amount: Nat,
}

/// ICRC1 Account
#[derive(CandidType, Deserialize, Debug, Clone)]
pub struct Account {
    pub owner: Principal,
    pub subaccount: Option<Vec<u8>>,
}

/// ICRC1 Transfer结果
#[derive(CandidType, Deserialize, Debug, Clone)]
pub enum TransferResult {
    #[serde(rename = "Ok")]
    Ok(Nat),
    #[serde(rename = "Err")]
    Err(TransferError),
}

/// ICRC1 Transfer错误
#[derive(CandidType, Deserialize, Debug, Clone)]
pub enum TransferError {
    BadFee { expected_fee: Nat },
    BadBurn { min_burn_amount: Nat },
    InsufficientFunds { balance: Nat },
    TooOld,
    CreatedInFuture { ledger_time: u64 },
    Duplicate { duplicate_of: Nat },
    TemporarilyUnavailable,
    GenericError { error_code: Nat, message: String },
}

/// ICPSwap V3 交易执行器
pub struct ICPSwapExecutor {
    icp_client: ICPClient,
    user_principal: Principal,
}

impl ICPSwapExecutor {
    /// 创建新的交易执行器
    pub fn new(icp_client: ICPClient, user_principal: Principal) -> Self {
        Self {
            icp_client,
            user_principal,
        }
    }
    
    /// 执行单个Swap交易
    pub async fn execute_swap(
        &self,
        pool_canister: Principal,
        amount_in: u128,
        zero_for_one: bool,
        amount_out_minimum: u128,
        slippage_tolerance: f64,
    ) -> Result<SwapResult> {
        info!("执行Swap交易: 池子={}, 输入={}, 方向={}, 最小输出={}", 
              pool_canister, amount_in, zero_for_one, amount_out_minimum);
        
        // 1. 准备Swap参数
        let swap_args = SwapArgs {
            amount_in: amount_in.to_string(),
            zero_for_one,
            amount_out_minimum: amount_out_minimum.to_string(),
        };
        
        // 2. 创建Pool Canister实例
        let pool_canister: Canister<ICPSwapResult<SwapResult>> = 
            self.icp_client.create_canister(pool_canister);
        
        // 3. 执行Swap调用
        let swap_result = self.icp_client.call_with_retry(|| {
            Box::pin(async {
                pool_canister.update("swap")
                    .with_arg(swap_args.clone())
                    .build()
                    .call()
                    .await
                    .map_err(|e| anyhow::anyhow!("Swap调用失败: {}", e))
            })
        }).await
        .context("执行Swap交易失败")?;
        
        // 4. 处理结果
        match swap_result {
            ICPSwapResult::Ok(result) => {
                info!("Swap交易成功: 输入={}, 输出={}", 
                      result.amount_in, result.amount_out);
                Ok(result)
            }
            ICPSwapResult::Err(err) => {
                error!("Swap交易失败: {:?}", err);
                Err(ArbitrageError::TradeExecutionError(
                    format!("Swap失败: {:?}", err)
                ))
            }
        }
    }
    
    /// 执行Token转账（用于准备Swap资金）
    pub async fn transfer_token(
        &self,
        token_canister: Principal,
        to: Principal,
        amount: u128,
        fee: Option<u128>,
    ) -> Result<Nat> {
        info!("执行Token转账: Token={}, 接收方={}, 金额={}", 
              token_canister, to, amount);
        
        // 1. 准备转账参数
        let transfer_args = TransferArgs {
            to: Account {
                owner: to,
                subaccount: None,
            },
            fee: fee.map(Nat::from),
            memo: None,
            from_subaccount: None,
            created_at_time: Some(
                std::time::SystemTime::now()
                    .duration_since(std::time::UNIX_EPOCH)
                    .unwrap()
                    .as_nanos() as u64
            ),
            amount: Nat::from(amount),
        };
        
        // 2. 创建Token Canister实例
        let token_canister: Canister<TransferResult> = 
            self.icp_client.create_canister(token_canister);
        
        // 3. 执行转账调用
        let transfer_result = self.icp_client.call_with_retry(|| {
            Box::pin(async {
                token_canister.update("icrc1_transfer")
                    .with_arg(transfer_args.clone())
                    .build()
                    .call()
                    .await
                    .map_err(|e| anyhow::anyhow!("转账调用失败: {}", e))
            })
        }).await
        .context("执行Token转账失败")?;
        
        // 4. 处理结果
        match transfer_result {
            TransferResult::Ok(block_index) => {
                info!("Token转账成功: 区块索引={}", block_index);
                Ok(block_index)
            }
            TransferResult::Err(err) => {
                error!("Token转账失败: {:?}", err);
                Err(ArbitrageError::TradeExecutionError(
                    format!("转账失败: {:?}", err)
                ))
            }
        }
    }
    
    /// 查询Token余额
    pub async fn get_token_balance(
        &self,
        token_canister: Principal,
        account: Principal,
    ) -> Result<Nat> {
        debug!("查询Token余额: Token={}, 账户={}", token_canister, account);
        
        // 创建Token Canister实例
        let token_canister: Canister<Nat> = 
            self.icp_client.create_canister(token_canister);
        
        // 准备账户参数
        let account_arg = Account {
            owner: account,
            subaccount: None,
        };
        
        // 查询余额
        let balance = self.icp_client.call_with_retry(|| {
            Box::pin(async {
                token_canister.query("icrc1_balance_of")
                    .with_arg(account_arg.clone())
                    .build()
                    .call()
                    .await
                    .map_err(|e| anyhow::anyhow!("余额查询失败: {}", e))
            })
        }).await
        .context("查询Token余额失败")?;
        
        debug!("Token余额: {}", balance);
        Ok(balance)
    }
    
    /// 执行套利交易序列
    pub async fn execute_arbitrage_sequence(
        &self,
        trades: Vec<ArbitrageTrade>,
    ) -> Result<ArbitrageExecutionResult> {
        info!("执行套利交易序列: {} 笔交易", trades.len());
        
        let mut results = Vec::new();
        let mut total_gas_used = 0u128;
        
        for (index, trade) in trades.iter().enumerate() {
            info!("执行第 {}/{} 笔交易", index + 1, trades.len());
            
            match self.execute_single_arbitrage_trade(trade).await {
                Ok(result) => {
                    total_gas_used += result.gas_used;
                    results.push(result);
                }
                Err(e) => {
                    error!("套利交易失败: {}", e);
                    return Err(e);
                }
            }
        }
        
        let total_profit = results.iter()
            .map(|r| r.profit)
            .sum::<i128>();
        
        info!("套利交易序列完成: 总利润={}, 总Gas={}", total_profit, total_gas_used);
        
        Ok(ArbitrageExecutionResult {
            trades: results,
            total_profit,
            total_gas_used,
            success: true,
        })
    }
    
    /// 执行单笔套利交易
    async fn execute_single_arbitrage_trade(
        &self,
        trade: &ArbitrageTrade,
    ) -> Result<TradeExecutionResult> {
        // 执行Swap
        let swap_result = self.execute_swap(
            trade.pool_canister,
            trade.amount_in,
            trade.zero_for_one,
            trade.amount_out_minimum,
            trade.slippage_tolerance,
        ).await?;
        
        // 计算利润（简化计算）
        let amount_in = swap_result.amount_in.0.to_u128().unwrap_or(0);
        let amount_out = swap_result.amount_out.0.to_u128().unwrap_or(0);
        let profit = amount_out as i128 - amount_in as i128;
        
        Ok(TradeExecutionResult {
            pool_canister: trade.pool_canister,
            amount_in,
            amount_out,
            profit,
            gas_used: 1000000, // 估算值
            success: true,
        })
    }
}

/// 套利交易参数
#[derive(Debug, Clone)]
pub struct ArbitrageTrade {
    pub pool_canister: Principal,
    pub amount_in: u128,
    pub zero_for_one: bool,
    pub amount_out_minimum: u128,
    pub slippage_tolerance: f64,
}

/// 交易执行结果
#[derive(Debug, Clone)]
pub struct TradeExecutionResult {
    pub pool_canister: Principal,
    pub amount_in: u128,
    pub amount_out: u128,
    pub profit: i128,
    pub gas_used: u128,
    pub success: bool,
}

/// 套利执行结果
#[derive(Debug, Clone)]
pub struct ArbitrageExecutionResult {
    pub trades: Vec<TradeExecutionResult>,
    pub total_profit: i128,
    pub total_gas_used: u128,
    pub success: bool,
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::network::{ICPConfig};
    
    #[tokio::test]
    async fn test_icpswap_executor_creation() {
        let icp_client = ICPClient::new(ICPConfig::default()).await.unwrap();
        let user_principal = Principal::from_text("rdmx6-jaaaa-aaaah-qcaiq-cai").unwrap();
        
        let executor = ICPSwapExecutor::new(icp_client, user_principal);
        assert_eq!(executor.user_principal, user_principal);
    }
}
