use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;
use rust_decimal::Decimal;
use num_traits::{FromPrimitive, ToPrimitive};

use crate::types::{ArbitrageOpportunity, ArbitrageResult, Result};
use crate::config::RiskConfig;

/// 风险管理器
pub struct RiskManager {
    config: RiskConfig,
    /// 当日交易统计
    daily_stats: Arc<RwLock<DailyStats>>,
    /// 紧急停止状态
    emergency_stop: Arc<RwLock<bool>>,
    /// 活跃交易
    active_trades: Arc<RwLock<HashMap<String, ArbitrageOpportunity>>>,
}

/// 当日统计数据
#[derive(Debug, Clone)]
struct DailyStats {
    total_trades: u64,
    total_profit: Decimal,
    total_loss: Decimal,
    net_profit: Decimal,
    largest_loss: Decimal,
    last_reset: u64, // 上次重置时间戳
}

impl DailyStats {
    fn new() -> Self {
        Self {
            total_trades: 0,
            total_profit: Decimal::ZERO,
            total_loss: Decimal::ZERO,
            net_profit: Decimal::ZERO,
            largest_loss: Decimal::ZERO,
            last_reset: std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap()
                .as_secs(),
        }
    }
    
    fn should_reset(&self) -> bool {
        let current_time = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_secs();
        
        // 如果超过24小时，重置统计
        current_time - self.last_reset > 24 * 60 * 60
    }
    
    fn reset(&mut self) {
        *self = Self::new();
    }
}

impl RiskManager {
    /// 创建新的风险管理器
    pub fn new(config: RiskConfig) -> Self {
        Self {
            config,
            daily_stats: Arc::new(RwLock::new(DailyStats::new())),
            emergency_stop: Arc::new(RwLock::new(false)),
            active_trades: Arc::new(RwLock::new(HashMap::new())),
        }
    }
    
    /// 评估套利机会的风险
    pub async fn assess_risk(&self, opportunity: &ArbitrageOpportunity) -> Result<RiskAssessment> {
        // 检查紧急停止状态
        if *self.emergency_stop.read().await {
            return Ok(RiskAssessment {
                approved: false,
                risk_level: RiskLevel::Critical,
                reason: "系统处于紧急停止状态".to_string(),
                max_position_size: Decimal::ZERO,
            });
        }
        
        // 检查代币黑名单
        if self.is_token_blacklisted(opportunity) {
            return Ok(RiskAssessment {
                approved: false,
                risk_level: RiskLevel::High,
                reason: "包含黑名单代币".to_string(),
                max_position_size: Decimal::ZERO,
            });
        }
        
        // 检查池子白名单
        if !self.is_pool_whitelisted(opportunity) {
            return Ok(RiskAssessment {
                approved: false,
                risk_level: RiskLevel::Medium,
                reason: "池子不在白名单中".to_string(),
                max_position_size: Decimal::ZERO,
            });
        }
        
        // 检查仓位大小
        let position_check = self.check_position_size(opportunity).await?;
        if !position_check.approved {
            return Ok(position_check);
        }
        
        // 检查日损失限制
        let daily_loss_check = self.check_daily_loss_limit(opportunity).await?;
        if !daily_loss_check.approved {
            return Ok(daily_loss_check);
        }
        
        // 检查置信度
        let confidence_check = self.check_confidence(opportunity);
        if !confidence_check.approved {
            return Ok(confidence_check);
        }
        
        // 计算综合风险等级
        let risk_level = self.calculate_risk_level(opportunity);
        
        Ok(RiskAssessment {
            approved: true,
            risk_level,
            reason: "风险评估通过".to_string(),
            max_position_size: self.calculate_max_position_size(opportunity).await,
        })
    }
    
    /// 检查代币是否在黑名单中
    fn is_token_blacklisted(&self, opportunity: &ArbitrageOpportunity) -> bool {
        for token in &opportunity.path.tokens {
            if self.config.blacklisted_tokens.contains(&token.address) {
                return true;
            }
        }
        false
    }
    
    /// 检查池子是否在白名单中
    fn is_pool_whitelisted(&self, opportunity: &ArbitrageOpportunity) -> bool {
        if self.config.whitelisted_pools.is_empty() {
            return true; // 如果没有设置白名单，默认允许所有池子
        }
        
        for pool_id in &opportunity.path.pools {
            if !self.config.whitelisted_pools.contains(pool_id) {
                return false;
            }
        }
        true
    }
    
    /// 检查仓位大小
    async fn check_position_size(&self, opportunity: &ArbitrageOpportunity) -> Result<RiskAssessment> {
        if opportunity.input_amount > self.config.max_position_size {
            return Ok(RiskAssessment {
                approved: false,
                risk_level: RiskLevel::High,
                reason: format!(
                    "交易金额 {} 超过最大仓位限制 {}",
                    opportunity.input_amount,
                    self.config.max_position_size
                ),
                max_position_size: self.config.max_position_size,
            });
        }
        
        Ok(RiskAssessment {
            approved: true,
            risk_level: RiskLevel::Low,
            reason: "仓位大小检查通过".to_string(),
            max_position_size: opportunity.input_amount,
        })
    }
    
    /// 检查日损失限制
    async fn check_daily_loss_limit(&self, opportunity: &ArbitrageOpportunity) -> Result<RiskAssessment> {
        let mut stats = self.daily_stats.write().await;
        
        // 检查是否需要重置统计
        if stats.should_reset() {
            stats.reset();
        }
        
        // 检查当前净损失
        if stats.net_profit < -self.config.max_daily_loss {
            return Ok(RiskAssessment {
                approved: false,
                risk_level: RiskLevel::Critical,
                reason: format!(
                    "当日净损失 {} 已达到限制 {}",
                    -stats.net_profit,
                    self.config.max_daily_loss
                ),
                max_position_size: Decimal::ZERO,
            });
        }
        
        // 检查潜在损失
        let potential_loss = opportunity.input_amount * self.config.stop_loss_percentage;
        if stats.net_profit - potential_loss < -self.config.max_daily_loss {
            return Ok(RiskAssessment {
                approved: false,
                risk_level: RiskLevel::High,
                reason: "潜在损失可能超过日损失限制".to_string(),
                max_position_size: Decimal::ZERO,
            });
        }
        
        Ok(RiskAssessment {
            approved: true,
            risk_level: RiskLevel::Low,
            reason: "日损失限制检查通过".to_string(),
            max_position_size: opportunity.input_amount,
        })
    }
    
    /// 检查置信度
    fn check_confidence(&self, opportunity: &ArbitrageOpportunity) -> RiskAssessment {
        if opportunity.confidence < 0.5 {
            return RiskAssessment {
                approved: false,
                risk_level: RiskLevel::High,
                reason: format!("置信度过低: {:.2}", opportunity.confidence),
                max_position_size: Decimal::ZERO,
            };
        }
        
        RiskAssessment {
            approved: true,
            risk_level: if opportunity.confidence > 0.8 {
                RiskLevel::Low
            } else {
                RiskLevel::Medium
            },
            reason: "置信度检查通过".to_string(),
            max_position_size: opportunity.input_amount,
        }
    }
    
    /// 计算风险等级（综合评估）
    fn calculate_risk_level(&self, opportunity: &ArbitrageOpportunity) -> RiskLevel {
        let mut risk_score = 0.0;

        // 1. 路径复杂度风险 (0-3分)
        let path_risk = match opportunity.path.pools.len() {
            2 => 0.0,
            3 => 1.0,
            4 => 2.0,
            _ => 3.0,
        };
        risk_score += path_risk;

        // 2. 利润率风险 (0-2分)
        let profit_percentage = opportunity.profit_percentage.to_f64().unwrap_or(0.0);
        let profit_risk = if profit_percentage < 0.005 { // 小于0.5%
            2.0
        } else if profit_percentage < 0.01 { // 小于1%
            1.0
        } else {
            0.0
        };
        risk_score += profit_risk;

        // 3. 置信度风险 (0-3分)
        let confidence_risk = if opportunity.confidence < 0.5 {
            3.0
        } else if opportunity.confidence < 0.7 {
            2.0
        } else if opportunity.confidence < 0.85 {
            1.0
        } else {
            0.0
        };
        risk_score += confidence_risk;

        // 4. 交易金额风险 (0-2分)
        let amount_ratio = opportunity.input_amount.to_f64().unwrap_or(0.0) /
            self.config.max_position_size.to_f64().unwrap_or(1.0);
        let amount_risk = if amount_ratio > 0.8 {
            2.0
        } else if amount_ratio > 0.5 {
            1.0
        } else {
            0.0
        };
        risk_score += amount_risk;

        // 5. 市场波动性风险 (0-1分)
        // 基于最近的价格变化估算
        let volatility_risk = if opportunity.path.pools.len() > 2 {
            0.5 // 多跳交易增加市场风险
        } else {
            0.0
        };
        risk_score += volatility_risk;

        // 6. 流动性风险 (0-2分)
        let liquidity_risk = if opportunity.input_amount > Decimal::from(50000) {
            1.5 // 大额交易流动性风险
        } else if opportunity.input_amount > Decimal::from(10000) {
            0.5
        } else {
            0.0
        };
        risk_score += liquidity_risk;

        // 根据总分确定风险等级
        match risk_score as i32 {
            0..=2 => RiskLevel::Low,
            3..=5 => RiskLevel::Medium,
            6..=8 => RiskLevel::High,
            _ => RiskLevel::Critical,
        }
    }
    
    /// 计算最大仓位大小
    async fn calculate_max_position_size(&self, opportunity: &ArbitrageOpportunity) -> Decimal {
        let base_limit = self.config.max_position_size;
        
        // 根据置信度调整
        let confidence_multiplier = Decimal::from_f64(opportunity.confidence).unwrap_or(Decimal::ONE);
        let adjusted_limit = base_limit * confidence_multiplier;
        
        // 根据路径长度调整
        let path_penalty = Decimal::from_f64(0.9_f64.powi(opportunity.path.pools.len() as i32))
            .unwrap_or(Decimal::ONE);
        let final_limit = adjusted_limit * path_penalty;
        
        final_limit.min(opportunity.input_amount)
    }
    
    /// 记录交易结果
    pub async fn record_trade_result(&self, result: &ArbitrageResult) -> Result<()> {
        let mut stats = self.daily_stats.write().await;
        
        // 检查是否需要重置统计
        if stats.should_reset() {
            stats.reset();
        }
        
        stats.total_trades += 1;
        
        if let Some(actual_profit) = result.actual_profit {
            if actual_profit > Decimal::ZERO {
                stats.total_profit += actual_profit;
            } else {
                let loss = -actual_profit;
                stats.total_loss += loss;
                stats.largest_loss = stats.largest_loss.max(loss);
                
                // 检查是否触发紧急停止
                if loss > self.config.max_position_size * self.config.stop_loss_percentage {
                    log::warn!("大额损失触发紧急停止: {}", loss);
                    *self.emergency_stop.write().await = true;
                }
            }
            
            stats.net_profit = stats.total_profit - stats.total_loss;
            
            // 检查日损失限制
            if stats.net_profit < -self.config.max_daily_loss {
                log::warn!("达到日损失限制，启动紧急停止");
                *self.emergency_stop.write().await = true;
            }
        }
        
        Ok(())
    }
    
    /// 添加活跃交易
    pub async fn add_active_trade(&self, opportunity: ArbitrageOpportunity) -> Result<()> {
        let mut active_trades = self.active_trades.write().await;
        active_trades.insert(opportunity.id.clone(), opportunity);
        Ok(())
    }
    
    /// 移除活跃交易
    pub async fn remove_active_trade(&self, opportunity_id: &str) -> Result<()> {
        let mut active_trades = self.active_trades.write().await;
        active_trades.remove(opportunity_id);
        Ok(())
    }
    
    /// 获取当日统计
    pub async fn get_daily_stats(&self) -> DailyStatsSnapshot {
        let stats = self.daily_stats.read().await;
        DailyStatsSnapshot {
            total_trades: stats.total_trades,
            total_profit: stats.total_profit,
            total_loss: stats.total_loss,
            net_profit: stats.net_profit,
            largest_loss: stats.largest_loss,
            success_rate: if stats.total_trades > 0 {
                (stats.total_profit / (stats.total_profit + stats.total_loss)).to_f64().unwrap_or(0.0)
            } else {
                0.0
            },
        }
    }
    
    /// 设置紧急停止
    pub async fn set_emergency_stop(&self, stop: bool) {
        *self.emergency_stop.write().await = stop;
        if stop {
            log::warn!("紧急停止已激活");
        } else {
            log::info!("紧急停止已解除");
        }
    }
    
    /// 检查是否处于紧急停止状态
    pub async fn is_emergency_stopped(&self) -> bool {
        *self.emergency_stop.read().await
    }
}

/// 风险评估结果
#[derive(Debug, Clone)]
pub struct RiskAssessment {
    pub approved: bool,
    pub risk_level: RiskLevel,
    pub reason: String,
    pub max_position_size: Decimal,
}

/// 风险等级
#[derive(Debug, Clone, PartialEq)]
pub enum RiskLevel {
    Low,
    Medium,
    High,
    Critical,
}

/// 当日统计快照
#[derive(Debug, Clone)]
pub struct DailyStatsSnapshot {
    pub total_trades: u64,
    pub total_profit: Decimal,
    pub total_loss: Decimal,
    pub net_profit: Decimal,
    pub largest_loss: Decimal,
    pub success_rate: f64,
}
