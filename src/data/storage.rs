use serde::{Deserialize, Serialize};
use std::path::Path;
use tokio::fs;
use crate::types::{PoolMetadata, Result, ArbitrageError};

/// 池子数据存储结构
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct PoolsData {
    pub pools: Vec<PoolInfo>,
    pub last_updated: u64,
    pub version: String,
}

/// 池子信息（从JSON文件加载）
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct PoolInfo {
    pub canister_id: String,
    pub metadata: PoolMetadata,
    pub is_active: bool,
    pub priority: u8, // 1-10, 10为最高优先级
}

/// 数据存储管理器
pub struct DataStorage {
    pools_file_path: String,
}

impl DataStorage {
    pub fn new(pools_file_path: String) -> Self {
        Self { pools_file_path }
    }
    
    /// 从JSON文件加载池子数据
    pub async fn load_pools(&self) -> Result<PoolsData> {
        if !Path::new(&self.pools_file_path).exists() {
            return Err(ArbitrageError::DataLoadError(
                format!("池子数据文件不存在: {}", self.pools_file_path)
            ));
        }
        
        let content = fs::read_to_string(&self.pools_file_path).await
            .map_err(|e| ArbitrageError::DataLoadError(
                format!("无法读取池子数据文件: {}", e)
            ))?;
        
        let pools_data: PoolsData = serde_json::from_str(&content)
            .map_err(|e| ArbitrageError::DataLoadError(
                format!("池子数据文件格式错误: {}", e)
            ))?;
        
        log::info!("成功加载 {} 个池子数据", pools_data.pools.len());
        Ok(pools_data)
    }
    
    /// 保存池子数据到JSON文件
    pub async fn save_pools(&self, pools_data: &PoolsData) -> Result<()> {
        let content = serde_json::to_string_pretty(pools_data)
            .map_err(|e| ArbitrageError::SerializationError(e))?;
        
        // 确保目录存在
        if let Some(parent) = Path::new(&self.pools_file_path).parent() {
            fs::create_dir_all(parent).await
                .map_err(|e| ArbitrageError::IoError(e))?;
        }
        
        fs::write(&self.pools_file_path, content).await
            .map_err(|e| ArbitrageError::IoError(e))?;
        
        log::info!("池子数据已保存到 {}", self.pools_file_path);
        Ok(())
    }
    
    /// 创建示例池子数据文件
    pub async fn create_sample_pools_file(&self) -> Result<()> {
        let sample_data = self.create_sample_pools_data();
        self.save_pools(&sample_data).await
    }
    
    /// 创建示例池子数据
    fn create_sample_pools_data(&self) -> PoolsData {
        use crate::types::{Token, PoolMetadata};
        
        let icp_token = Token {
            address: "rrkah-fqaaa-aaaaa-aaaaq-cai".to_string(),
            standard: "ICRC1".to_string(),
            symbol: "ICP".to_string(),
            decimals: 8,
        };
        
        let ckbtc_token = Token {
            address: "mxzaz-hqaaa-aaaar-qaada-cai".to_string(),
            standard: "ICRC1".to_string(),
            symbol: "ckBTC".to_string(),
            decimals: 8,
        };
        
        let cketh_token = Token {
            address: "ss2fx-dyaaa-aaaar-qacoq-cai".to_string(),
            standard: "ICRC1".to_string(),
            symbol: "ckETH".to_string(),
            decimals: 18,
        };
        
        let pools = vec![
            PoolInfo {
                canister_id: "pool1-canister-id".to_string(),
                metadata: PoolMetadata {
                    key: "ICP-ckBTC-3000".to_string(),
                    token0: icp_token.clone(),
                    token1: ckbtc_token.clone(),
                    fee: 3000, // 0.3%
                    tick: 0,
                    liquidity: 1000000000000000000,
                    sqrt_price_x96: 79228162514264337593543950336,
                    max_liquidity_per_tick: 11505743598341114571880798222544994,
                    next_position_id: 1,
                },
                is_active: true,
                priority: 9,
            },
            PoolInfo {
                canister_id: "pool2-canister-id".to_string(),
                metadata: PoolMetadata {
                    key: "ICP-ckETH-3000".to_string(),
                    token0: icp_token.clone(),
                    token1: cketh_token.clone(),
                    fee: 3000,
                    tick: 0,
                    liquidity: 500000000000000000,
                    sqrt_price_x96: 79228162514264337593543950336,
                    max_liquidity_per_tick: 11505743598341114571880798222544994,
                    next_position_id: 1,
                },
                is_active: true,
                priority: 8,
            },
            PoolInfo {
                canister_id: "pool3-canister-id".to_string(),
                metadata: PoolMetadata {
                    key: "ckBTC-ckETH-500".to_string(),
                    token0: ckbtc_token,
                    token1: cketh_token,
                    fee: 500, // 0.05%
                    tick: 0,
                    liquidity: 2000000000000000000,
                    sqrt_price_x96: 79228162514264337593543950336,
                    max_liquidity_per_tick: 11505743598341114571880798222544994,
                    next_position_id: 1,
                },
                is_active: true,
                priority: 10,
            },
        ];
        
        PoolsData {
            pools,
            last_updated: std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap()
                .as_secs(),
            version: "1.0.0".to_string(),
        }
    }
    
    /// 验证池子数据的完整性
    pub fn validate_pools_data(&self, pools_data: &PoolsData) -> Result<()> {
        if pools_data.pools.is_empty() {
            return Err(ArbitrageError::DataLoadError("池子数据为空".to_string()));
        }
        
        // 检查重复的canister_id
        let mut canister_ids = std::collections::HashSet::new();
        for pool in &pools_data.pools {
            if !canister_ids.insert(&pool.canister_id) {
                return Err(ArbitrageError::DataLoadError(
                    format!("发现重复的canister_id: {}", pool.canister_id)
                ));
            }
        }
        
        // 检查池子元数据的有效性
        for pool in &pools_data.pools {
            if pool.metadata.fee > 100000 { // 最大10%手续费
                return Err(ArbitrageError::DataLoadError(
                    format!("池子 {} 的手续费过高: {}", pool.canister_id, pool.metadata.fee)
                ));
            }
            
            if pool.metadata.token0.address == pool.metadata.token1.address {
                return Err(ArbitrageError::DataLoadError(
                    format!("池子 {} 的token0和token1相同", pool.canister_id)
                ));
            }
        }
        
        log::info!("池子数据验证通过，共 {} 个池子", pools_data.pools.len());
        Ok(())
    }
}
