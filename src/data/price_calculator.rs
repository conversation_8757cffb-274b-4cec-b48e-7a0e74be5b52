use crate::types::{PoolState, Result, ArbitrageError};

// 简单的u256类型定义（用于高精度计算）
type u256 = u128;

/// Swap结果结构体
#[derive(Debug, Clone)]
pub struct SwapResult {
    pub amount_out: f64,
    pub new_sqrt_price_x96: u128,
    pub price_impact: f64,
    pub fee_amount: f64,
}

/// 价格计算器 - 基于UniswapV3算法
pub struct PriceCalculator;

impl PriceCalculator {
    /// 从sqrtPriceX96计算价格（基于ICPSwap V3实现）
    pub fn sqrt_price_x96_to_price(sqrt_price_x96: u128, decimals0: u8, decimals1: u8) -> Result<f64> {
        if sqrt_price_x96 == 0 {
            return Err(ArbitrageError::PriceCalculationError("sqrtPriceX96不能为0".to_string()));
        }

        // ICPSwap V3使用的公式: price = (sqrtPriceX96 / 2^96)^2
        // 使用高精度计算避免精度损失
        let sqrt_price_f64 = sqrt_price_x96 as f64;
        let q96_f64 = 2_f64.powi(96); // 2^96 = 79228162514264337593543950336

        // 计算 sqrtPrice = sqrtPriceX96 / 2^96
        let sqrt_price = sqrt_price_f64 / q96_f64;

        // 计算 price = sqrtPrice^2
        let price = sqrt_price * sqrt_price;

        // 调整小数位数差异
        // 注意：这里的调整是为了得到token1/token0的价格
        let decimal_diff = decimals1 as i32 - decimals0 as i32;
        let decimal_adjustment = 10_f64.powi(decimal_diff);

        let final_price = price * decimal_adjustment;

        // 确保价格在合理范围内
        let bounded_price = final_price.max(1e-30).min(1e30);

        Ok(bounded_price)
    }
    
    /// 从tick计算价格（基于ICPSwap V3的TickMath实现）
    pub fn tick_to_price(tick: i32, decimals0: u8, decimals1: u8) -> Result<f64> {
        // ICPSwap V3使用: price = 1.0001^tick
        // 这与UniswapV3的实现一致
        if tick < -887272 || tick > 887272 {
            return Err(ArbitrageError::PriceCalculationError("tick超出有效范围".to_string()));
        }

        let base = 1.0001f64;
        let price = base.powi(tick);

        // 调整小数位数差异
        let decimal_adjustment = if decimals0 >= decimals1 {
            10f64.powi((decimals0 - decimals1) as i32)
        } else {
            1.0 / 10f64.powi((decimals1 - decimals0) as i32)
        };

        let final_price = price * decimal_adjustment;

        // 确保价格在合理范围内
        let bounded_price = final_price.max(1e-18).min(1e18);

        Ok(bounded_price)
    }
    
    /// 计算给定输入金额的输出金额（需要指定交易方向）
    /// 这个函数已废弃，请使用 calculate_amount_out_with_direction
    #[deprecated(note = "请使用 calculate_amount_out_with_direction 指定交易方向")]
    pub fn calculate_amount_out(
        amount_in: f64,
        pool_state: &PoolState,
    ) -> Result<f64> {
        // 默认假设是token0换token1，但这是不准确的
        Self::calculate_amount_out_with_direction(amount_in, pool_state, true)
    }

    /// 计算给定输入金额的输出金额（基于ICPSwap V3的SwapMath实现，指定交易方向）
    pub fn calculate_amount_out_with_direction(
        amount_in: f64,
        pool_state: &PoolState,
        zero_for_one: bool, // true表示token0换token1，false表示token1换token0
    ) -> Result<f64> {
        if amount_in <= 0.0 {
            return Err(ArbitrageError::PriceCalculationError("输入金额必须大于0".to_string()));
        }

        let liquidity = pool_state.metadata.liquidity;
        if liquidity == 0 {
            return Err(ArbitrageError::PriceCalculationError("池子流动性为0".to_string()));
        }

        // 计算手续费（ICPSwap V3中手续费以百万分之一为单位）
        let fee_rate = pool_state.metadata.fee as f64 / 1_000_000.0;
        let amount_in_after_fee = amount_in * (1.0 - fee_rate);

        // 使用ICPSwap V3的集中流动性计算
        let sqrt_price_x96 = pool_state.metadata.sqrt_price_x96 as f64;
        let q96 = 2_f64.powi(96);
        let sqrt_price_current = sqrt_price_x96 / q96;
        let liquidity_f64 = liquidity as f64;

        // 计算当前价格 (token1/token0)
        let current_price = sqrt_price_current * sqrt_price_current;

        // 根据交易方向计算输出金额
        let amount_out_f64 = if zero_for_one {
            // token0 -> token1: 使用当前价格
            // 输出 = 输入 * 价格
            amount_in_after_fee * current_price
        } else {
            // token1 -> token0: 使用反向价格
            // 输出 = 输入 / 价格
            amount_in_after_fee / current_price
        };

        // 考虑流动性深度的价格影响
        let trade_size_ratio = amount_in_after_fee / liquidity_f64;
        let price_impact_factor = 1.0 - (trade_size_ratio * 0.1).min(0.5); // 限制最大影响50%
        let adjusted_amount_out = amount_out_f64 * price_impact_factor;

        // 确保输出金额为正数
        let bounded_amount_out = adjusted_amount_out.max(0.0);

        Ok(bounded_amount_out)
    }

    /// 根据输入和输出代币确定交易方向并计算输出金额
    pub fn calculate_amount_out_by_tokens(
        amount_in: f64,
        pool_state: &PoolState,
        token_in: &crate::types::Token,
        token_out: &crate::types::Token,
    ) -> Result<f64> {
        // 确定交易方向
        let zero_for_one = if token_in.address == pool_state.metadata.token0.address {
            // 输入token0，输出token1
            if token_out.address != pool_state.metadata.token1.address {
                return Err(ArbitrageError::PriceCalculationError(
                    "输出代币与池子token1不匹配".to_string()
                ));
            }
            true
        } else if token_in.address == pool_state.metadata.token1.address {
            // 输入token1，输出token0
            if token_out.address != pool_state.metadata.token0.address {
                return Err(ArbitrageError::PriceCalculationError(
                    "输出代币与池子token0不匹配".to_string()
                ));
            }
            false
        } else {
            return Err(ArbitrageError::PriceCalculationError(
                "输入代币不属于此池子".to_string()
            ));
        };

        Self::calculate_amount_out_with_direction(amount_in, pool_state, zero_for_one)
    }
    
    /// 计算滑点（基于价格影响）
    pub fn calculate_slippage(amount_in: f64, pool_state: &PoolState) -> Result<f64> {
        if pool_state.metadata.liquidity == 0 {
            return Err(ArbitrageError::PriceCalculationError("池子流动性为0".to_string()));
        }

        // 计算价格影响作为滑点的近似
        let liquidity_f64 = pool_state.metadata.liquidity as f64;

        // 滑点 ≈ 交易量 / (2 * 流动性)，这是基于恒定乘积公式的近似
        let slippage_f64 = amount_in / (2.0 * liquidity_f64);

        // 考虑手续费对滑点的影响
        let fee_rate = pool_state.metadata.fee as f64 / 1000000.0;
        let total_slippage = slippage_f64 + fee_rate;

        // 限制最大滑点为50%（极端情况）
        Ok(total_slippage.min(0.5))
    }
    
    /// 使用三分搜索找到最优交易金额（最大化净利润）
    pub fn find_optimal_trade_amount(
        pool_state: &PoolState,
        max_amount: f64,
        min_profit_threshold: f64,
    ) -> Result<f64> {
        let mut left = 0.001; // 最小交易量
        let mut right = max_amount;
        let epsilon = 0.0001; // 提高精度
        let max_iterations = 100;
        let mut iterations = 0;

        // 三分搜索寻找最大净利润
        while right - left > epsilon && iterations < max_iterations {
            let delta = (right - left) / 3.0;
            let mid1 = left + delta;
            let mid2 = right - delta;

            let net_profit1 = Self::calculate_net_profit(mid1, pool_state)?;
            let net_profit2 = Self::calculate_net_profit(mid2, pool_state)?;

            if net_profit1 < net_profit2 {
                left = mid1;
            } else {
                right = mid2;
            }

            iterations += 1;
        }

        let optimal_amount = (left + right) / 2.0;
        let optimal_profit = Self::calculate_net_profit(optimal_amount, pool_state)?;

        // 检查是否满足最小利润要求
        if optimal_profit < min_profit_threshold {
            return Ok(0.0); // 没有满足条件的交易量
        }

        Ok(optimal_amount)
    }
    
    /// 计算净利润（考虑交易成本）
    fn calculate_net_profit(
        amount_in: f64,
        pool_state: &PoolState,
    ) -> Result<f64> {
        if amount_in <= 0.0 {
            return Ok(0.0);
        }

        let amount_out = Self::calculate_amount_out(amount_in, pool_state)?;

        // 估算交易成本（Gas费用等）
        let transaction_cost = Self::estimate_transaction_cost(amount_in)?;

        // 净利润 = 输出 - 输入 - 交易成本
        let gross_profit = amount_out - amount_in;
        let net_profit = gross_profit - transaction_cost;

        Ok(net_profit)
    }

    /// 估算交易成本
    fn estimate_transaction_cost(amount_in: f64) -> Result<f64> {
        // 基础交易成本 + 按金额比例的成本
        let base_cost = 0.01; // 基础成本
        let proportional_cost = amount_in * 0.0001; // 0.01%

        Ok(base_cost + proportional_cost)
    }
    
    /// 计算两个池子之间的价格差异
    pub fn calculate_price_difference(
        pool1: &PoolState,
        pool2: &PoolState,
    ) -> Result<f64> {
        if pool1.price <= 0.0 || pool2.price <= 0.0 {
            return Err(ArbitrageError::PriceCalculationError("价格不能为0或负数".to_string()));
        }

        let price_diff = (pool1.price - pool2.price).abs();
        let avg_price = (pool1.price + pool2.price) / 2.0;

        Ok(price_diff / avg_price)
    }
    
    /// 验证价格的合理性
    pub fn validate_price(price: f64, min_price: f64, max_price: f64) -> Result<()> {
        if price <= 0.0 {
            return Err(ArbitrageError::PriceCalculationError("价格必须大于0".to_string()));
        }

        if price < min_price || price > max_price {
            return Err(ArbitrageError::PriceCalculationError(
                format!("价格 {} 超出合理范围 [{}, {}]", price, min_price, max_price)
            ));
        }

        Ok(())
    }

    /// 计算精确的swap输出（基于ICPSwap V3的真实SwapMath.computeSwapStep实现）
    pub fn calculate_exact_swap_output(
        amount_in: Decimal,
        pool_state: &PoolState,
        zero_for_one: bool, // true表示token0换token1
    ) -> Result<SwapResult> {
        if amount_in <= Decimal::ZERO {
            return Err(ArbitrageError::PriceCalculationError("输入金额必须大于0".to_string()));
        }

        let liquidity = pool_state.metadata.liquidity;
        if liquidity == 0 {
            return Err(ArbitrageError::PriceCalculationError("池子流动性为0".to_string()));
        }

        // 转换为f64进行计算
        let amount_in_f64 = amount_in.to_f64().unwrap_or(0.0);
        let sqrt_price_current_x96 = pool_state.metadata.sqrt_price_x96 as f64;
        let liquidity_f64 = liquidity as f64;
        let fee_pips = pool_state.metadata.fee as f64;

        // 基于ICPSwap V3的SwapMath.computeSwapStep实现
        let nat1e6 = 1_000_000.0;

        // 1. 计算扣除手续费后的金额
        // amountRemainingLessFee = amountRemaining * (1000000 - feePips) / 1000000
        let amount_remaining_less_fee = amount_in_f64 * (nat1e6 - fee_pips) / nat1e6;

        // 2. 计算当前sqrtPrice（标准化）
        let q96 = 2_f64.powi(96);
        let sqrt_price_current = sqrt_price_current_x96 / q96;

        // 3. 计算新的sqrtPrice（基于ICPSwap V3的getNextSqrtPriceFromInput）
        let sqrt_price_next = if zero_for_one {
            // token0 -> token1: getNextSqrtPriceFromAmount0RoundingUp
            // sqrtPriceNext = (liquidity * sqrtPrice) / (liquidity + amount * sqrtPrice)
            let numerator = liquidity_f64 * sqrt_price_current;
            let denominator = liquidity_f64 + amount_remaining_less_fee * sqrt_price_current;
            if denominator > 0.0 {
                numerator / denominator
            } else {
                sqrt_price_current
            }
        } else {
            // token1 -> token0: getNextSqrtPriceFromAmount1RoundingDown
            // sqrtPriceNext = sqrtPrice + (amount * Q96) / liquidity
            sqrt_price_current + (amount_remaining_less_fee * q96) / (liquidity_f64 * q96)
        };

        // 4. 计算输出金额（基于ICPSwap V3的getAmountDelta）
        let amount_out_f64 = if zero_for_one {
            // token0 -> token1: getAmount1DeltaNat
            // amount1 = liquidity * |sqrtPriceB - sqrtPriceA| / Q96
            // 注意：这里需要除以Q96，因为sqrtPrice是X96格式
            let sqrt_price_current_x96 = sqrt_price_current * q96;
            let sqrt_price_next_x96 = sqrt_price_next * q96;
            liquidity_f64 * (sqrt_price_current_x96 - sqrt_price_next_x96).abs() / q96
        } else {
            // token1 -> token0: getAmount0DeltaNat
            // amount0 = liquidity * |1/sqrtPriceA - 1/sqrtPriceB| * Q96
            if sqrt_price_current > 0.0 && sqrt_price_next > 0.0 {
                let sqrt_price_current_x96 = sqrt_price_current * q96;
                let sqrt_price_next_x96 = sqrt_price_next * q96;
                liquidity_f64 * (1.0 / sqrt_price_next_x96 - 1.0 / sqrt_price_current_x96).abs() * q96
            } else {
                0.0
            }
        };

        // 5. 计算价格影响（基于实际交易价格与市场价格的差异）
        let market_price = sqrt_price_current * sqrt_price_current; // 当前市场价格

        // 计算实际交易的执行价格
        let execution_price = if zero_for_one {
            // token0 -> token1: 执行价格 = amount_out / amount_in_after_fee
            if amount_remaining_less_fee > 0.0 {
                amount_out_f64 / amount_remaining_less_fee
            } else {
                market_price
            }
        } else {
            // token1 -> token0: 执行价格 = amount_in_after_fee / amount_out
            if amount_out_f64 > 0.0 {
                amount_remaining_less_fee / amount_out_f64
            } else {
                market_price
            }
        };

        // 价格影响 = |执行价格 - 市场价格| / 市场价格
        let price_impact = if market_price > 0.0 {
            ((execution_price - market_price) / market_price).abs()
        } else {
            0.0
        };

        // 6. 计算新的sqrtPriceX96
        let new_sqrt_price_x96 = (sqrt_price_next * q96) as u128;

        // 7. 计算手续费
        let fee_amount_f64 = amount_in_f64 * fee_pips / nat1e6;

        // 转换回Decimal
        let amount_out = Decimal::from_f64(amount_out_f64.max(0.0))
            .ok_or_else(|| ArbitrageError::PriceCalculationError("无法转换输出金额".to_string()))?;

        let fee_amount = Decimal::from_f64(fee_amount_f64)
            .unwrap_or(Decimal::ZERO);

        let price_impact_decimal = Decimal::from_f64(price_impact)
            .unwrap_or(Decimal::ZERO);

        Ok(SwapResult {
            amount_out,
            new_sqrt_price_x96,
            price_impact: price_impact_decimal,
            fee_amount,
        })
    }

    /// 根据输入金额计算新的sqrtPrice（基于ICPSwap V3的getNextSqrtPriceFromInput）
    fn get_next_sqrt_price_from_input(
        sqrt_price_current: f64,
        liquidity: f64,
        amount_in: f64,
        zero_for_one: bool,
    ) -> f64 {
        if zero_for_one {
            // token0 -> token1: getNextSqrtPriceFromAmount0RoundingUp
            // sqrt_price_next = (L * sqrt_price) / (L + amount_in * sqrt_price)
            let numerator = liquidity * sqrt_price_current;
            let denominator = liquidity + amount_in * sqrt_price_current;
            if denominator > 0.0 {
                numerator / denominator
            } else {
                sqrt_price_current
            }
        } else {
            // token1 -> token0: getNextSqrtPriceFromAmount1RoundingDown
            // sqrt_price_next = sqrt_price + amount_in / L
            let q96 = 2_f64.powi(96);
            let quotient = amount_in * q96 / liquidity;
            sqrt_price_current + quotient / q96
        }
    }

    /// 计算token0的数量变化（基于ICPSwap V3的getAmount0DeltaNat）
    fn calc_amount0_delta(sqrt_price_a: f64, sqrt_price_b: f64, liquidity: f64) -> f64 {
        // 确保 sqrt_price_a <= sqrt_price_b
        let (sqrt_ratio_a, sqrt_ratio_b) = if sqrt_price_a > sqrt_price_b {
            (sqrt_price_b, sqrt_price_a)
        } else {
            (sqrt_price_a, sqrt_price_b)
        };

        if sqrt_ratio_a <= 0.0 {
            return 0.0;
        }

        // amount0 = L * (1/sqrt_ratio_a - 1/sqrt_ratio_b)
        // 注意：这里的sqrt_price已经是标准化的（除以Q96后的值）
        liquidity * (1.0 / sqrt_ratio_a - 1.0 / sqrt_ratio_b)
    }

    /// 计算token1的数量变化（基于ICPSwap V3的getAmount1DeltaNat）
    fn calc_amount1_delta(sqrt_price_a: f64, sqrt_price_b: f64, liquidity: f64) -> f64 {
        // 确保 sqrt_price_a <= sqrt_price_b
        let (sqrt_ratio_a, sqrt_ratio_b) = if sqrt_price_a > sqrt_price_b {
            (sqrt_price_b, sqrt_price_a)
        } else {
            (sqrt_price_a, sqrt_price_b)
        };

        // amount1 = L * (sqrt_ratio_b - sqrt_ratio_a)
        // 注意：这里的sqrt_price已经是标准化的（除以Q96后的值）
        liquidity * (sqrt_ratio_b - sqrt_ratio_a)
    }

    /// 基于ICPSwap V3的getNextSqrtPriceFromInput实现
    fn get_next_sqrt_price_from_input_icpswap(
        sqrt_price_x96: u128,
        liquidity: u128,
        amount_in: u128,
        zero_for_one: bool,
    ) -> Result<u128> {
        if sqrt_price_x96 == 0 || liquidity == 0 {
            return Err(ArbitrageError::PriceCalculationError("非法参数".to_string()));
        }

        if zero_for_one {
            // getNextSqrtPriceFromAmount0RoundingUp
            Self::get_next_sqrt_price_from_amount0_rounding_up(sqrt_price_x96, liquidity, amount_in, true)
        } else {
            // getNextSqrtPriceFromAmount1RoundingDown
            Self::get_next_sqrt_price_from_amount1_rounding_down(sqrt_price_x96, liquidity, amount_in, true)
        }
    }

    /// 基于ICPSwap V3的getNextSqrtPriceFromAmount0RoundingUp实现
    fn get_next_sqrt_price_from_amount0_rounding_up(
        sqrt_price_x96: u128,
        liquidity: u128,
        amount: u128,
        add: bool,
    ) -> Result<u128> {
        if amount == 0 {
            return Ok(sqrt_price_x96);
        }

        let q96 = 1u128 << 96;
        let numerator1 = (liquidity as u128) << 96;

        if add {
            // 使用安全的乘法检查溢出
            if let Some(product) = amount.checked_mul(sqrt_price_x96) {
                if let Some(denominator) = numerator1.checked_add(product) {
                    if denominator >= numerator1 {
                        return Ok(Self::mul_div_rounding_up(numerator1, sqrt_price_x96, denominator));
                    }
                }
            }
            // 溢出情况的处理
            Ok(Self::div_rounding_up(numerator1, numerator1 / sqrt_price_x96 + amount))
        } else {
            // 使用安全的乘法检查溢出
            if let Some(product) = amount.checked_mul(sqrt_price_x96) {
                if numerator1 > product {
                    let denominator = numerator1 - product;
                    return Ok(Self::mul_div_rounding_up(numerator1, sqrt_price_x96, denominator));
                }
            }
            Err(ArbitrageError::PriceCalculationError("非法参数".to_string()))
        }
    }

    /// 基于ICPSwap V3的getNextSqrtPriceFromAmount1RoundingDown实现
    fn get_next_sqrt_price_from_amount1_rounding_down(
        sqrt_price_x96: u128,
        liquidity: u128,
        amount: u128,
        add: bool,
    ) -> Result<u128> {
        let q96 = 1u128 << 96;

        if add {
            let quotient = if amount <= u128::MAX >> 96 {
                (amount << 96) / liquidity
            } else {
                Self::mul_div(amount, q96, liquidity)
            };
            Ok(sqrt_price_x96 + quotient)
        } else {
            let quotient = Self::mul_div_rounding_up(amount, q96, liquidity);
            if sqrt_price_x96 <= quotient {
                return Err(ArbitrageError::PriceCalculationError("非法参数".to_string()));
            }
            Ok(sqrt_price_x96 - quotient)
        }
    }

    /// 基于ICPSwap V3的getAmount0DeltaNat实现
    fn get_amount0_delta_nat_icpswap(
        sqrt_ratio_a_x96: u128,
        sqrt_ratio_b_x96: u128,
        liquidity: u128,
        round_up: bool,
    ) -> Result<u128> {
        let (sqrt_ratio_a, sqrt_ratio_b) = if sqrt_ratio_a_x96 > sqrt_ratio_b_x96 {
            (sqrt_ratio_b_x96, sqrt_ratio_a_x96)
        } else {
            (sqrt_ratio_a_x96, sqrt_ratio_b_x96)
        };

        if sqrt_ratio_a == 0 {
            return Err(ArbitrageError::PriceCalculationError("非法参数".to_string()));
        }

        let q96 = 1u128 << 96;
        let numerator1 = (liquidity as u128) << 96;
        let numerator2 = sqrt_ratio_b - sqrt_ratio_a;

        if round_up {
            let temp_result = Self::mul_div_rounding_up(numerator1, numerator2, sqrt_ratio_b);
            Ok(Self::div_rounding_up(temp_result, sqrt_ratio_a))
        } else {
            let md = Self::mul_div(numerator1, numerator2, sqrt_ratio_b);
            Ok(md / sqrt_ratio_a)
        }
    }

    /// 基于ICPSwap V3的getAmount1DeltaNat实现
    fn get_amount1_delta_nat_icpswap(
        sqrt_ratio_a_x96: u128,
        sqrt_ratio_b_x96: u128,
        liquidity: u128,
        round_up: bool,
    ) -> Result<u128> {
        let (sqrt_ratio_a, sqrt_ratio_b) = if sqrt_ratio_a_x96 > sqrt_ratio_b_x96 {
            (sqrt_ratio_b_x96, sqrt_ratio_a_x96)
        } else {
            (sqrt_ratio_a_x96, sqrt_ratio_b_x96)
        };

        let q96 = 1u128 << 96;
        let delta = sqrt_ratio_b - sqrt_ratio_a;

        if round_up {
            Ok(Self::mul_div_rounding_up(liquidity as u128, delta, q96))
        } else {
            Ok(Self::mul_div(liquidity as u128, delta, q96))
        }
    }

    /// 精确的mulDiv实现（使用f64进行高精度计算）
    fn mul_div(a: u128, b: u128, denominator: u128) -> u128 {
        if denominator == 0 {
            return 0;
        }
        // 使用f64进行计算，对于我们的用例来说精度足够
        let result = (a as f64) * (b as f64) / (denominator as f64);
        result as u128
    }

    /// 向上舍入的mulDiv实现
    fn mul_div_rounding_up(a: u128, b: u128, denominator: u128) -> u128 {
        if denominator == 0 {
            return 0;
        }
        // 使用f64进行计算，然后向上舍入
        let result = (a as f64) * (b as f64) / (denominator as f64);
        result.ceil() as u128
    }

    /// 向上舍入的除法实现
    fn div_rounding_up(numerator: u128, denominator: u128) -> u128 {
        if denominator == 0 {
            return 0;
        }
        let quotient = numerator / denominator;
        let remainder = numerator % denominator;

        if remainder > 0 {
            quotient + 1
        } else {
            quotient
        }
    }

    /// 计算tick对应的sqrtPriceX96
    pub fn tick_to_sqrt_price_x96(tick: i32) -> Result<u128> {
        if tick < -887272 || tick > 887272 {
            return Err(ArbitrageError::PriceCalculationError("tick超出有效范围".to_string()));
        }

        // 使用1.0001^(tick/2)计算sqrtPrice
        let sqrt_price = 1.0001_f64.powf(tick as f64 / 2.0);
        let q96 = 2_f64.powi(96);
        let sqrt_price_x96 = sqrt_price * q96;

        Ok(sqrt_price_x96 as u128)
    }

    /// 计算sqrtPriceX96对应的tick
    pub fn sqrt_price_x96_to_tick(sqrt_price_x96: u128) -> Result<i32> {
        if sqrt_price_x96 == 0 {
            return Err(ArbitrageError::PriceCalculationError("sqrtPriceX96不能为0".to_string()));
        }

        let q96 = 2_f64.powi(96);
        let sqrt_price = sqrt_price_x96 as f64 / q96;

        // tick = 2 * log(sqrtPrice) / log(1.0001)
        let tick = 2.0 * sqrt_price.ln() / 1.0001_f64.ln();

        Ok(tick.round() as i32)
    }
}



