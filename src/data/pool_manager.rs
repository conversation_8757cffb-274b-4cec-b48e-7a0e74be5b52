use std::sync::Arc;
use tokio::sync::RwLock;
use tokio::time::{Duration, interval};
use dashmap::DashMap;
use rust_decimal::Decimal;

use crate::types::{
    PoolState, PoolMetadata, Token, TradingPair, Result, ArbitrageError
};
use crate::data::{DataStorage, PoolsData, PriceCalculator};
use crate::config::Settings;

/// 池子管理器 - 负责管理所有交易池的状态和数据
pub struct PoolManager {
    /// 池子状态缓存 (canister_id -> PoolState)
    pools: Arc<DashMap<String, PoolState>>,
    /// 交易对到池子的映射 (TradingPair -> Vec<canister_id>)
    pair_to_pools: Arc<DashMap<TradingPair, Vec<String>>>,
    /// 数据存储
    storage: DataStorage,
    /// 配置
    settings: Settings,
    /// 最后同步时间
    last_sync: Arc<RwLock<u64>>,
}

impl PoolManager {
    /// 创建新的池子管理器
    pub fn new(settings: Settings) -> Self {
        let storage = DataStorage::new(settings.data.pools_file_path.clone());
        
        Self {
            pools: Arc::new(DashMap::new()),
            pair_to_pools: Arc::new(DashMap::new()),
            storage,
            settings,
            last_sync: Arc::new(RwLock::new(0)),
        }
    }
    
    /// 初始化池子管理器
    pub async fn initialize(&self) -> Result<()> {
        log::info!("正在初始化池子管理器...");
        
        // 加载池子数据
        let pools_data = match self.storage.load_pools().await {
            Ok(data) => {
                self.storage.validate_pools_data(&data)?;
                data
            },
            Err(ArbitrageError::DataLoadError(_)) => {
                log::warn!("池子数据文件不存在，创建示例文件");
                self.storage.create_sample_pools_file().await?;
                self.storage.load_pools().await?
            },
            Err(e) => return Err(e),
        };
        
        // 初始化池子状态
        self.initialize_pools(&pools_data).await?;
        
        // 启动定期同步任务
        self.start_sync_task().await;
        
        log::info!("池子管理器初始化完成，加载了 {} 个池子", self.pools.len());
        Ok(())
    }
    
    /// 初始化池子状态
    async fn initialize_pools(&self, pools_data: &PoolsData) -> Result<()> {
        for pool_info in &pools_data.pools {
            if !pool_info.is_active {
                continue;
            }
            
            // 计算价格
            let price = PriceCalculator::sqrt_price_x96_to_price(
                pool_info.metadata.sqrt_price_x96,
                pool_info.metadata.token0.decimals,
                pool_info.metadata.token1.decimals,
            )?;
            
            let inverse_price = if price > Decimal::ZERO {
                Decimal::ONE / price
            } else {
                Decimal::ZERO
            };
            
            // 创建池子状态
            let pool_state = PoolState {
                metadata: pool_info.metadata.clone(),
                price,
                inverse_price,
                last_updated: std::time::SystemTime::now()
                    .duration_since(std::time::UNIX_EPOCH)
                    .unwrap()
                    .as_secs(),
                canister_id: pool_info.canister_id.clone(),
            };
            
            // 添加到缓存
            self.pools.insert(pool_info.canister_id.clone(), pool_state);
            
            // 更新交易对映射
            self.update_pair_mapping(&pool_info.metadata, &pool_info.canister_id);
        }
        
        Ok(())
    }
    
    /// 更新交易对映射
    fn update_pair_mapping(&self, metadata: &PoolMetadata, canister_id: &str) {
        // token0 -> token1
        let pair1 = TradingPair::new(metadata.token0.clone(), metadata.token1.clone());
        self.pair_to_pools.entry(pair1)
            .or_insert_with(Vec::new)
            .push(canister_id.to_string());
        
        // token1 -> token0
        let pair2 = TradingPair::new(metadata.token1.clone(), metadata.token0.clone());
        self.pair_to_pools.entry(pair2)
            .or_insert_with(Vec::new)
            .push(canister_id.to_string());
    }
    
    /// 启动定期同步任务
    async fn start_sync_task(&self) {
        let _pools = Arc::clone(&self.pools);
        let sync_interval = self.settings.data.sync_interval_secs;
        let last_sync = Arc::clone(&self.last_sync);
        
        tokio::spawn(async move {
            let mut interval = interval(Duration::from_secs(sync_interval));
            
            loop {
                interval.tick().await;
                
                log::debug!("开始同步池子数据...");
                
                // 这里应该调用IC网络获取最新的池子状态
                // 由于这是示例，我们只更新时间戳
                let current_time = std::time::SystemTime::now()
                    .duration_since(std::time::UNIX_EPOCH)
                    .unwrap()
                    .as_secs();
                
                *last_sync.write().await = current_time;
                
                log::debug!("池子数据同步完成");
            }
        });
    }
    
    /// 获取池子状态
    pub fn get_pool(&self, canister_id: &str) -> Option<PoolState> {
        self.pools.get(canister_id).map(|entry| entry.clone())
    }
    
    /// 获取所有池子
    pub fn get_all_pools(&self) -> Vec<PoolState> {
        self.pools.iter().map(|entry| entry.value().clone()).collect()
    }
    
    /// 根据交易对获取池子
    pub fn get_pools_for_pair(&self, pair: &TradingPair) -> Vec<PoolState> {
        if let Some(pool_ids) = self.pair_to_pools.get(pair) {
            pool_ids.iter()
                .filter_map(|id| self.get_pool(id))
                .collect()
        } else {
            Vec::new()
        }
    }
    
    /// 获取支持指定代币的所有池子
    pub fn get_pools_with_token(&self, token: &Token) -> Vec<PoolState> {
        self.pools.iter()
            .filter(|entry| {
                let pool = entry.value();
                pool.metadata.token0 == *token || pool.metadata.token1 == *token
            })
            .map(|entry| entry.value().clone())
            .collect()
    }
    
    /// 更新池子价格
    pub async fn update_pool_price(&self, canister_id: &str, new_sqrt_price_x96: u128) -> Result<()> {
        if let Some(mut pool_entry) = self.pools.get_mut(canister_id) {
            let pool = pool_entry.value_mut();
            
            // 计算新价格
            let new_price = PriceCalculator::sqrt_price_x96_to_price(
                new_sqrt_price_x96,
                pool.metadata.token0.decimals,
                pool.metadata.token1.decimals,
            )?;
            
            // 检查价格变化是否超过阈值
            let price_change = (new_price - pool.price).abs() / pool.price;
            if price_change >= self.settings.data.price_update_threshold {
                pool.price = new_price;
                pool.inverse_price = if new_price > Decimal::ZERO {
                    Decimal::ONE / new_price
                } else {
                    Decimal::ZERO
                };
                pool.metadata.sqrt_price_x96 = new_sqrt_price_x96;
                pool.last_updated = std::time::SystemTime::now()
                    .duration_since(std::time::UNIX_EPOCH)
                    .unwrap()
                    .as_secs();
                
                log::debug!("池子 {} 价格更新: {} -> {}", canister_id, pool.price, new_price);
            }
        }
        
        Ok(())
    }
    
    /// 获取所有支持的代币
    pub fn get_all_tokens(&self) -> Vec<Token> {
        let mut tokens = std::collections::HashSet::new();
        
        for pool in self.pools.iter() {
            tokens.insert(pool.metadata.token0.clone());
            tokens.insert(pool.metadata.token1.clone());
        }
        
        tokens.into_iter().collect()
    }
    
    /// 检查池子是否活跃
    pub fn is_pool_active(&self, canister_id: &str) -> bool {
        if let Some(pool) = self.get_pool(canister_id) {
            let current_time = std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap()
                .as_secs();
            
            // 如果超过缓存TTL时间没有更新，认为不活跃
            current_time - pool.last_updated <= self.settings.data.cache_ttl_secs
        } else {
            false
        }
    }
    
    /// 获取池子统计信息
    pub async fn get_stats(&self) -> PoolManagerStats {
        let total_pools = self.pools.len();
        let active_pools = self.pools.iter()
            .filter(|entry| self.is_pool_active(entry.key()))
            .count();

        let total_pairs = self.pair_to_pools.len();

        PoolManagerStats {
            total_pools,
            active_pools,
            total_pairs,
            last_sync: *self.last_sync.read().await,
        }
    }
}

/// 池子管理器统计信息
#[derive(Debug, Clone)]
pub struct PoolManagerStats {
    pub total_pools: usize,
    pub active_pools: usize,
    pub total_pairs: usize,
    pub last_sync: u64,
}
